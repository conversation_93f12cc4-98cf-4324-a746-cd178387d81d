import 'package:flutter/material.dart';

class AppIcons {
  // الأيقونات الرئيسية
  static const IconData dashboard = Icons.dashboard;
  static const IconData inventory = Icons.inventory;
  static const IconData transactions = Icons.swap_horiz;
  static const IconData reports = Icons.assessment;
  static const IconData users = Icons.people;
  static const IconData settings = Icons.settings;

  // أيقونات الأصناف
  static const IconData addItem = Icons.add_box;
  static const IconData editItem = Icons.edit;
  static const IconData deleteItem = Icons.delete;
  static const IconData searchItem = Icons.search;
  static const IconData filterItem = Icons.filter_list;
  static const IconData itemDetails = Icons.info;
  static const IconData itemLocation = Icons.location_on;
  static const IconData itemCategory = Icons.category;
  static const IconData itemQuantity = Icons.straighten;
  static const IconData itemUnit = Icons.scale;

  // أيقونات الحركات
  static const IconData incoming = Icons.keyboard_arrow_down;
  static const IconData outgoing = Icons.keyboard_arrow_up;
  static const IconData consumption = Icons.trending_down;
  static const IconData adjustment = Icons.tune;
  static const IconData workOrder = Icons.work;
  static const IconData requestForm = Icons.description;
  static const IconData responsiblePerson = Icons.person;
  static const IconData transactionDate = Icons.calendar_today;
  static const IconData transactionNotes = Icons.note;

  // أيقونات التقارير
  static const IconData chart = Icons.bar_chart;
  static const IconData pieChart = Icons.pie_chart;
  static const IconData lineChart = Icons.show_chart;
  static const IconData export = Icons.file_download;
  static const IconData import = Icons.file_upload;
  static const IconData print = Icons.print;
  static const IconData download = Icons.download;
  static const IconData share = Icons.share;

  // أيقونات المستخدمين
  static const IconData addUser = Icons.person_add;
  static const IconData editUser = Icons.edit;
  static const IconData deleteUser = Icons.person_remove;
  static const IconData userRole = Icons.admin_panel_settings;
  static const IconData userEmail = Icons.email;
  static const IconData userPassword = Icons.lock;
  static const IconData userActive = Icons.check_circle;
  static const IconData userInactive = Icons.cancel;

  // أيقونات الحالة
  static const IconData success = Icons.check_circle;
  static const IconData error = Icons.error;
  static const IconData warning = Icons.warning;
  static const IconData info = Icons.info;
  static const IconData loading = Icons.hourglass_empty;
  static const IconData done = Icons.done;
  static const IconData close = Icons.close;
  static const IconData back = Icons.arrow_back;
  static const IconData forward = Icons.arrow_forward;
  static const IconData refresh = Icons.refresh;

  // أيقونات المخزون
  static const IconData stockNormal = Icons.check_circle;
  static const IconData stockLow = Icons.warning;
  static const IconData stockOut = Icons.cancel;
  static const IconData stockAlert = Icons.notifications;
  static const IconData stockHistory = Icons.history;
  static const IconData stockMovement = Icons.swap_vert;

  // أيقونات التنقل
  static const IconData home = Icons.home;
  static const IconData menu = Icons.menu;
  static const IconData more = Icons.more_vert;
  static const IconData expand = Icons.expand_more;
  static const IconData collapse = Icons.expand_less;
  static const IconData next = Icons.navigate_next;
  static const IconData previous = Icons.navigate_before;

  // أيقونات الإعدادات
  static const IconData language = Icons.language;
  static const IconData theme = Icons.palette;
  static const IconData notifications = Icons.notifications;
  static const IconData security = Icons.security;
  static const IconData backup = Icons.backup;
  static const IconData restore = Icons.restore;
  static const IconData about = Icons.info;
  static const IconData help = Icons.help;
  static const IconData feedback = Icons.feedback;

  // أيقونات إضافية
  static const IconData calendar = Icons.calendar_today;
  static const IconData time = Icons.access_time;
  static const IconData location = Icons.location_on;
  static const IconData phone = Icons.phone;
  static const IconData email = Icons.email;
  static const IconData web = Icons.web;
  static const IconData link = Icons.link;
  static const IconData attachment = Icons.attach_file;
  static const IconData image = Icons.image;
  static const IconData video = Icons.video_library;
  static const IconData audio = Icons.audiotrack;
  static const IconData document = Icons.description;
  static const IconData folder = Icons.folder;
  static const IconData file = Icons.insert_drive_file;
}
