import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core/service_locator.dart';
import '../services/logging_service.dart';

class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  late final LoggingService _loggingService;

  void initialize() {
    _loggingService = serviceLocator<LoggingService>();

    // Set up global error handling
    FlutterError.onError = _handleFlutterError;
    PlatformDispatcher.instance.onError = _handlePlatformError;
  }

  void _handleFlutterError(FlutterErrorDetails details) {
    _loggingService.logError(
      'Flutter Error: ${details.exception}',
      error: details.exception,
      stackTrace: details.stack,
      tag: 'FlutterError',
    );

    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  bool _handlePlatformError(Object error, StackTrace stack) {
    _loggingService.logError(
      'Platform Error: $error',
      error: error,
      stackTrace: stack,
      tag: 'PlatformError',
    );

    return true; // Indicates that the error was handled
  }

  // Standardized error handling for different types of errors
  AppError handleError(dynamic error,
      {String? context, StackTrace? stackTrace}) {
    final appError = _categorizeError(error, context);

    _loggingService.logError(
      appError.message,
      error: error,
      stackTrace: stackTrace,
      tag: appError.type.toString(),
    );

    return appError;
  }

  AppError _categorizeError(dynamic error, String? context) {
    if (error is NetworkError) {
      return error;
    } else if (error is DatabaseError) {
      return error;
    } else if (error is ValidationError) {
      return error;
    } else if (error is AuthenticationError) {
      return error;
    } else if (error is TimeoutException) {
      return NetworkError(
        message: 'Request timeout. Please check your internet connection.',
        originalError: error,
        context: context,
      );
    } else if (error is FormatException) {
      return ValidationError(
        message: 'Invalid data format: ${error.message}',
        originalError: error,
        context: context,
      );
    } else {
      return UnknownError(
        message: 'An unexpected error occurred: ${error.toString()}',
        originalError: error,
        context: context,
      );
    }
  }

  // Show user-friendly error messages
  void showErrorToUser(BuildContext context, AppError error) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isArabic ? error.arabicMessage ?? error.message : error.message,
        ),
        backgroundColor: _getErrorColor(error.type),
        behavior: SnackBarBehavior.floating,
        action: error.isRetryable
            ? SnackBarAction(
                label: isArabic ? 'إعادة المحاولة' : 'Retry',
                onPressed: error.retryAction ?? () {},
              )
            : null,
      ),
    );
  }

  Color _getErrorColor(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Colors.orange;
      case ErrorType.database:
        return Colors.red;
      case ErrorType.validation:
        return Colors.amber;
      case ErrorType.authentication:
        return Colors.deepOrange;
      case ErrorType.unknown:
        return Colors.grey;
    }
  }

  // Async error wrapper
  Future<T> handleAsync<T>(
    Future<T> Function() operation, {
    String? context,
    T? fallbackValue,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      final appError =
          handleError(error, context: context, stackTrace: stackTrace);

      if (fallbackValue != null) {
        return fallbackValue;
      }

      throw appError;
    }
  }

  // Sync error wrapper
  T handleSync<T>(
    T Function() operation, {
    String? context,
    T? fallbackValue,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      final appError =
          handleError(error, context: context, stackTrace: stackTrace);

      if (fallbackValue != null) {
        return fallbackValue;
      }

      throw appError;
    }
  }
}

// Base error class
abstract class AppError implements Exception {
  final String message;
  final String? arabicMessage;
  final ErrorType type;
  final dynamic originalError;
  final String? context;
  final bool isRetryable;
  final VoidCallback? retryAction;

  const AppError({
    required this.message,
    this.arabicMessage,
    required this.type,
    this.originalError,
    this.context,
    this.isRetryable = false,
    this.retryAction,
  });

  @override
  String toString() => 'AppError: $message';
}

enum ErrorType {
  network,
  database,
  validation,
  authentication,
  unknown,
}

// Specific error types
class NetworkError extends AppError {
  const NetworkError({
    required String message,
    String? arabicMessage,
    dynamic originalError,
    String? context,
    bool isRetryable = true,
    VoidCallback? retryAction,
  }) : super(
          message: message,
          arabicMessage: arabicMessage,
          type: ErrorType.network,
          originalError: originalError,
          context: context,
          isRetryable: isRetryable,
          retryAction: retryAction,
        );
}

class DatabaseError extends AppError {
  const DatabaseError({
    required String message,
    String? arabicMessage,
    dynamic originalError,
    String? context,
    bool isRetryable = false,
    VoidCallback? retryAction,
  }) : super(
          message: message,
          arabicMessage: arabicMessage,
          type: ErrorType.database,
          originalError: originalError,
          context: context,
          isRetryable: isRetryable,
          retryAction: retryAction,
        );
}

class ValidationError extends AppError {
  const ValidationError({
    required String message,
    String? arabicMessage,
    dynamic originalError,
    String? context,
    bool isRetryable = false,
    VoidCallback? retryAction,
  }) : super(
          message: message,
          arabicMessage: arabicMessage,
          type: ErrorType.validation,
          originalError: originalError,
          context: context,
          isRetryable: isRetryable,
          retryAction: retryAction,
        );
}

class AuthenticationError extends AppError {
  const AuthenticationError({
    required String message,
    String? arabicMessage,
    dynamic originalError,
    String? context,
    bool isRetryable = false,
    VoidCallback? retryAction,
  }) : super(
          message: message,
          arabicMessage: arabicMessage,
          type: ErrorType.authentication,
          originalError: originalError,
          context: context,
          isRetryable: isRetryable,
          retryAction: retryAction,
        );
}

class UnknownError extends AppError {
  const UnknownError({
    required String message,
    String? arabicMessage,
    dynamic originalError,
    String? context,
    bool isRetryable = false,
    VoidCallback? retryAction,
  }) : super(
          message: message,
          arabicMessage: arabicMessage,
          type: ErrorType.unknown,
          originalError: originalError,
          context: context,
          isRetryable: isRetryable,
          retryAction: retryAction,
        );
}

// Mixin for widgets to handle errors consistently
mixin ErrorHandlingMixin<T extends StatefulWidget> on State<T> {
  void handleError(dynamic error, {String? errorContext}) {
    final appError = ErrorHandler().handleError(error, context: errorContext);
    ErrorHandler().showErrorToUser(context, appError);
  }

  Future<R?> handleAsyncOperation<R>(
    Future<R> Function() operation, {
    String? errorContext,
    bool showErrorToUser = true,
  }) async {
    try {
      return await operation();
    } catch (error) {
      if (showErrorToUser) {
        handleError(error, errorContext: errorContext);
      }
      return null;
    }
  }
}
