import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';

enum CardVariant {
  elevated,
  outlined,
  filled,
}

class ModernCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final CardVariant variant;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? elevation;
  final Color? backgroundColor;
  final Color? borderColor;
  final BorderRadius? borderRadius;
  final bool isInteractive;

  const ModernCard({
    Key? key,
    required this.child,
    this.onTap,
    this.variant = CardVariant.elevated,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.isInteractive = false,
  }) : super(key: key);

  const ModernCard.elevated({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    EdgeInsets? padding,
    EdgeInsets? margin,
    double? elevation,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    bool isInteractive = false,
  }) : this(
          key: key,
          child: child,
          onTap: onTap,
          variant: CardVariant.elevated,
          padding: padding,
          margin: margin,
          elevation: elevation,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          isInteractive: isInteractive,
        );

  const ModernCard.outlined({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    bool isInteractive = false,
  }) : this(
          key: key,
          child: child,
          onTap: onTap,
          variant: CardVariant.outlined,
          padding: padding,
          margin: margin,
          backgroundColor: backgroundColor,
          borderColor: borderColor,
          borderRadius: borderRadius,
          isInteractive: isInteractive,
        );

  const ModernCard.filled({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    EdgeInsets? padding,
    EdgeInsets? margin,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    bool isInteractive = false,
  }) : this(
          key: key,
          child: child,
          onTap: onTap,
          variant: CardVariant.filled,
          padding: padding,
          margin: margin,
          backgroundColor: backgroundColor,
          borderRadius: borderRadius,
          isInteractive: isInteractive,
        );

  @override
  State<ModernCard> createState() => _ModernCardState();
}

class _ModernCardState extends State<ModernCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppSpacing.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    final baseElevation = _getElevation();
    _elevationAnimation = Tween<double>(
      begin: baseElevation,
      end: (baseElevation + 2).clamp(0.0, 24.0), // Clamp to reasonable values
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.isInteractive || widget.onTap != null) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.isInteractive || widget.onTap != null) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.isInteractive || widget.onTap != null) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: widget.margin ?? AppSpacing.cardPaddingAll,
            decoration: _getDecoration(),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onTap,
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                borderRadius: _getBorderRadius(),
                child: Container(
                  padding: widget.padding ?? AppSpacing.cardPaddingAll,
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _getDecoration() {
    switch (widget.variant) {
      case CardVariant.elevated:
        return BoxDecoration(
          color: widget.backgroundColor ?? AppColors.surface,
          borderRadius: _getBorderRadius(),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: _elevationAnimation.value.clamp(0.0, 24.0),
              offset:
                  Offset(0, (_elevationAnimation.value / 2).clamp(0.0, 12.0)),
            ),
          ],
        );
      case CardVariant.outlined:
        return BoxDecoration(
          color: widget.backgroundColor ?? AppColors.surface,
          borderRadius: _getBorderRadius(),
          border: Border.all(
            color: widget.borderColor ?? AppColors.outline,
            width: 1,
          ),
        );
      case CardVariant.filled:
        return BoxDecoration(
          color: widget.backgroundColor ?? AppColors.surfaceVariant,
          borderRadius: _getBorderRadius(),
        );
    }
  }

  BorderRadius _getBorderRadius() {
    return widget.borderRadius ?? AppSpacing.borderRadiusLg;
  }

  double _getElevation() {
    if (widget.elevation != null) return widget.elevation!;

    switch (widget.variant) {
      case CardVariant.elevated:
        return AppSpacing.elevation2;
      case CardVariant.outlined:
      case CardVariant.filled:
        return AppSpacing.elevation0;
    }
  }
}

// Specialized card components
class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? iconColor;
  final String? subtitle;
  final VoidCallback? onTap;
  final Widget? trailing;

  const StatCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    this.iconColor,
    this.subtitle,
    this.onTap,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return ModernCard.elevated(
      onTap: onTap,
      isInteractive: onTap != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: AppSpacing.paddingSm,
                decoration: BoxDecoration(
                  color:
                      (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                  borderRadius: AppSpacing.borderRadiusSm,
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? AppColors.primary,
                  size: AppSpacing.iconMd,
                ),
              ),
              const Spacer(),
              if (trailing != null) trailing!,
            ],
          ),
          AppSpacing.verticalSpaceMd,
          Text(
            value,
            style: AppTypography.headlineMedium(context, isArabic: isArabic)
                .copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          AppSpacing.verticalSpaceXs,
          Text(
            title,
            style:
                AppTypography.bodyMedium(context, isArabic: isArabic).copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          if (subtitle != null) ...[
            AppSpacing.verticalSpaceXs,
            Text(
              subtitle!,
              style:
                  AppTypography.bodySmall(context, isArabic: isArabic).copyWith(
                color: AppColors.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class InfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final List<Widget>? actions;

  const InfoCard({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return ModernCard.outlined(
      onTap: onTap,
      isInteractive: onTap != null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (leading != null) ...[
                leading!,
                AppSpacing.horizontalSpaceMd,
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.titleMedium(context,
                          isArabic: isArabic),
                    ),
                    if (subtitle != null) ...[
                      AppSpacing.verticalSpaceXs,
                      Text(
                        subtitle!,
                        style: AppTypography.bodyMedium(context,
                                isArabic: isArabic)
                            .copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (trailing != null) trailing!,
            ],
          ),
          if (actions != null && actions!.isNotEmpty) ...[
            AppSpacing.verticalSpaceMd,
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }
}
