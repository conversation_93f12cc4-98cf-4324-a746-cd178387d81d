import '../models/user.dart';
import '../services/database_service.dart';
import '../services/cache_service.dart';
import '../services/logging_service.dart';
import 'item_repository.dart';

class UserRepository {
  final DatabaseService _databaseService;
  final CacheService _cacheService;
  final LoggingService _loggingService;

  UserRepository(
      this._databaseService, this._cacheService, this._loggingService);

  Future<List<User>> getAllUsers() async {
    final stopwatch = Stopwatch()..start();

    try {
      final cachedUsers =
          _cacheService.getMemoryCache<List<User>>(CacheKeys.allUsers);

      if (cachedUsers != null) {
        _loggingService.logCacheHit(CacheKeys.allUsers);
        return cachedUsers;
      }

      _loggingService.logCacheMiss(CacheKeys.allUsers);

      final users = await _databaseService.getAllUsers();

      _cacheService.setMemoryCache(
        CacheKeys.allUsers,
        users,
        duration: _cacheService.getCacheDuration(CacheType.users),
      );

      _loggingService.logPerformance('getAllUsers', stopwatch.elapsed);
      return users;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get all users',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<PaginatedResult<User>> getUsers({
    int page = 1,
    int pageSize = 20,
    UserRole? role,
    bool? isActive,
    String? searchQuery,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey =
          _buildCacheKey('users', page, pageSize, role, isActive, searchQuery);
      final cachedResult =
          _cacheService.getMemoryCache<PaginatedResult<User>>(cacheKey);

      if (cachedResult != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedResult;
      }

      _loggingService.logCacheMiss(cacheKey);

      final result =
          await _getUsersPaginated(page, pageSize, role, isActive, searchQuery);

      _cacheService.setMemoryCache(
        cacheKey,
        result,
        duration: _cacheService.getCacheDuration(CacheType.users),
      );

      _loggingService.logPerformance('getUsers', stopwatch.elapsed);
      return result;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get users',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<PaginatedResult<User>> _getUsersPaginated(
    int page,
    int pageSize,
    UserRole? role,
    bool? isActive,
    String? searchQuery,
  ) async {
    final supabase = _databaseService.supabase;
    final offset = (page - 1) * pageSize;

    // Build query
    var query = supabase.from('users').select('*');

    // Apply filters
    if (role != null) {
      query = query.eq('role', role.toString().split('.').last);
    }

    if (isActive != null) {
      query = query.eq('is_active', isActive);
    }

    if (searchQuery != null && searchQuery.isNotEmpty) {
      query =
          query.or('name.ilike.%$searchQuery%,username.ilike.%$searchQuery%');
    }

    // Apply pagination and ordering
    final response = await query
        .order('created_at', ascending: false)
        .range(offset, offset + pageSize - 1);

    final users =
        (response as List<dynamic>).map((json) => User.fromJson(json)).toList();

    // Get total count with a separate query
    final countResponse = await supabase.from('users').select('id').count();
    final totalCount = countResponse.count;

    return PaginatedResult<User>(
      items: users,
      totalCount: totalCount,
      currentPage: page,
      pageSize: pageSize,
    );
  }

  Future<User?> getUserById(String id) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = 'user_$id';
      final cachedUser = _cacheService.getMemoryCache<User>(cacheKey);

      if (cachedUser != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedUser;
      }

      _loggingService.logCacheMiss(cacheKey);

      final user = await _databaseService.getUserById(id);

      if (user != null) {
        _cacheService.setMemoryCache(
          cacheKey,
          user,
          duration: _cacheService.getCacheDuration(CacheType.users),
        );
      }

      _loggingService.logPerformance('getUserById', stopwatch.elapsed);
      return user;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get user by id',
          error: e, stackTrace: stackTrace);
      return null;
    } finally {
      stopwatch.stop();
    }
  }

  Future<User?> getUserByUsername(String username) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = 'user_username_$username';
      final cachedUser = _cacheService.getMemoryCache<User>(cacheKey);

      if (cachedUser != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedUser;
      }

      _loggingService.logCacheMiss(cacheKey);

      final user = await _databaseService.getUserByUsername(username);

      if (user != null) {
        _cacheService.setMemoryCache(
          cacheKey,
          user,
          duration: _cacheService.getCacheDuration(CacheType.users),
        );
      }

      _loggingService.logPerformance('getUserByUsername', stopwatch.elapsed);
      return user;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get user by username',
          error: e, stackTrace: stackTrace);
      return null;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> insertUser(User user) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.insertUser(user);
      _invalidateUserCaches();
      _loggingService.logInfo('User inserted: ${user.username}');
      _loggingService.logPerformance('insertUser', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to insert user',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> updateUser(User user) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.updateUser(user);
      _invalidateUserCaches();
      _cacheService
          .clearMemoryCache('user_${user.id}'); // Clear specific user cache
      _cacheService.clearMemoryCache(
          'user_username_${user.username}'); // Clear username cache
      _loggingService.logInfo('User updated: ${user.username}');
      _loggingService.logPerformance('updateUser', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to update user',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> deleteUser(String id) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.deleteUser(id);
      _invalidateUserCaches();
      _cacheService.clearMemoryCache('user_$id'); // Clear specific user cache
      _loggingService.logInfo('User deleted: $id');
      _loggingService.logPerformance('deleteUser', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to delete user',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<Map<String, dynamic>> getUserStats() async {
    final stopwatch = Stopwatch()..start();

    try {
      const cacheKey = 'user_stats';
      final cachedStats =
          _cacheService.getMemoryCache<Map<String, dynamic>>(cacheKey);

      if (cachedStats != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedStats;
      }

      _loggingService.logCacheMiss(cacheKey);

      final users = await getAllUsers();

      final stats = {
        'totalUsers': users.length,
        'activeUsers': users.where((u) => u.isActive).length,
        'inactiveUsers': users.where((u) => !u.isActive).length,
        'adminUsers': users.where((u) => u.role == UserRole.admin).length,
        'managerUsers': users.where((u) => u.role == UserRole.manager).length,
        'regularUsers': users.where((u) => u.role == UserRole.user).length,
        'accountantUsers':
            users.where((u) => u.role == UserRole.accountant).length,
      };

      _cacheService.setMemoryCache(
        cacheKey,
        stats,
        duration: _cacheService.getCacheDuration(CacheType.users),
      );

      _loggingService.logPerformance('getUserStats', stopwatch.elapsed);
      return stats;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get user stats',
          error: e, stackTrace: stackTrace);
      return {};
    } finally {
      stopwatch.stop();
    }
  }

  void _invalidateUserCaches() {
    _cacheService.invalidateCache('users');
    _cacheService.invalidateCache('user_stats');
  }

  String _buildCacheKey(String prefix, int page, int pageSize, UserRole? role,
      bool? isActive, String? searchQuery) {
    final parts = [prefix, page.toString(), pageSize.toString()];
    if (role != null) parts.add('role_${role.toString().split('.').last}');
    if (isActive != null) parts.add('active_$isActive');
    if (searchQuery != null) parts.add('search_$searchQuery');
    return parts.join('_');
  }
}
