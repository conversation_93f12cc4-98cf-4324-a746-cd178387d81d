import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'constants/app_colors.dart';
import 'constants/app_texts.dart';
import 'constants/app_theme.dart';
import 'services/database_service.dart';
import 'services/auth_service.dart';
import 'services/cache_service.dart';
import 'services/logging_service.dart';
import 'core/service_locator.dart';
import 'utils/error_handler.dart';
import 'utils/performance_monitor.dart';
import 'utils/memory_manager.dart';
import 'views/auth/login_view.dart';
import 'views/dashboard/optimized_dashboard_view.dart';
import 'views/items/optimized_item_list_view.dart';
import 'views/transactions/transaction_list_view.dart';
import 'views/reports/reports_view.dart';
import 'views/users/users_view.dart';
import 'views/settings/settings_view.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Supabase
  await Supabase.initialize(
    url: 'https://wwkyridwmjiwadmszsuw.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind3a3lyaWR3bWppd2FkbXN6c3V3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMjY3OTQsImV4cCI6MjA2NjcwMjc5NH0.zagW7eud5uV1B4EUv3IJg2MKgycLkiuTcQKU586_6vo',
  );

  // تهيئة Service Locator
  await setupServiceLocator();

  // تهيئة الخدمات
  await serviceLocator<CacheService>().initialize();
  await serviceLocator<LoggingService>().initialize();

  // تهيئة أدوات الأداء والذاكرة
  ErrorHandler().initialize();
  PerformanceMonitor().initialize();
  MemoryManager().initialize();

  serviceLocator<LoggingService>()
      .logInfo('All services initialized successfully');

  // تهيئة مجلدات التطبيق
  try {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/backups');
    final exportDir = Directory('${appDir.path}/exports');

    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    if (!await exportDir.exists()) {
      await exportDir.create(recursive: true);
    }

    serviceLocator<LoggingService>()
        .logInfo('Application directories initialized successfully');
    serviceLocator<LoggingService>()
        .logInfo('Backup directory: ${backupDir.path}');
    serviceLocator<LoggingService>()
        .logInfo('Export directory: ${exportDir.path}');
  } catch (e) {
    serviceLocator<LoggingService>()
        .logError('Failed to initialize application directories', error: e);
  }

  // اختبار قاعدة البيانات
  serviceLocator<LoggingService>().logInfo('=== Database Testing ===');
  final authService = AuthService();

  try {
    // إنشاء المستخدم الافتراضي
    serviceLocator<LoggingService>().logInfo('Creating default user...');
    await authService.createDefaultUser();
    serviceLocator<LoggingService>()
        .logInfo('Default user created successfully');

    // التحقق من وجود المستخدم
    serviceLocator<LoggingService>().logInfo('Checking for existing users...');
    final hasUsers = await authService.hasUsers();
    if (hasUsers) {
      serviceLocator<LoggingService>().logInfo('Users exist in the system');

      // اختبار البحث عن المستخدم
      final user = await authService.login('admin', 'admin123');
      if (user != null) {
        serviceLocator<LoggingService>().logInfo('Login test successful');
        serviceLocator<LoggingService>().logInfo(
            'User info: ID=${user.id}, Name=${user.name}, Username=${user.username}, Role=${user.role}, Active=${user.isActive}');
      } else {
        serviceLocator<LoggingService>().logError('Login test failed');
      }
    } else {
      serviceLocator<LoggingService>().logError('No users found in the system');
    }

    // إنشاء بيانات تجريبية
    serviceLocator<LoggingService>().logInfo('Creating sample data...');
    final dbService = DatabaseService();
    await dbService.createSampleData();
    serviceLocator<LoggingService>()
        .logInfo('Sample data created successfully');
  } catch (e) {
    serviceLocator<LoggingService>().logError('Database test failed', error: e);
  }

  runApp(const InventoryApp());
}

class InventoryApp extends StatefulWidget {
  const InventoryApp({Key? key}) : super(key: key);

  @override
  State<InventoryApp> createState() => _InventoryAppState();
}

class _InventoryAppState extends State<InventoryApp> {
  Locale _locale = const Locale('ar');

  @override
  void initState() {
    super.initState();
  }

  void setLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppTexts.appTitle,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.getThemeForLocale(_locale),

      // ===== دعم اللغات =====
      locale: _locale,
      supportedLocales: const [Locale('ar'), Locale('en')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      localeResolutionCallback: (locale, supportedLocales) {
        if (locale == null) return supportedLocales.first;
        for (var supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale.languageCode) {
            return supportedLocale;
          }
        }
        return supportedLocales.first;
      },

      // ===== دعم RTL =====
      builder: (context, child) {
        return Directionality(
          textDirection: _locale.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },

      // ===== الصفحة الرئيسية والروابط =====
      home: LoginView(onLocaleChange: setLocale),
      routes: {
        '/login': (context) => LoginView(onLocaleChange: setLocale),
        '/dashboard': (context) => const OptimizedDashboardView(),
        '/items': (context) => const OptimizedItemListView(),
        '/transactions': (context) => const TransactionListView(),
        '/reports': (context) => const ReportsView(),
        '/users': (context) => const UsersView(),
        '/settings': (context) => const SettingsView(),
      },
    );
  }
}
