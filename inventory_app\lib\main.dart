import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path_provider/path_provider.dart';
import 'constants/app_colors.dart';
import 'constants/app_texts.dart';
import 'services/database_service.dart';
import 'services/auth_service.dart';
import 'services/cache_service.dart';
import 'services/logging_service.dart';
import 'core/service_locator.dart';
import 'utils/error_handler.dart';
import 'utils/performance_monitor.dart';
import 'utils/memory_manager.dart';
import 'views/auth/login_view.dart';
import 'views/dashboard/optimized_dashboard_view.dart';
import 'views/items/optimized_item_list_view.dart';
import 'views/transactions/transaction_list_view.dart';
import 'views/reports/reports_view.dart';
import 'views/users/users_view.dart';
import 'views/settings/settings_view.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Supabase
  await Supabase.initialize(
    url: 'https://wwkyridwmjiwadmszsuw.supabase.co',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind3a3lyaWR3bWppd2FkbXN6c3V3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMjY3OTQsImV4cCI6MjA2NjcwMjc5NH0.zagW7eud5uV1B4EUv3IJg2MKgycLkiuTcQKU586_6vo',
  );

  // تهيئة Service Locator
  await setupServiceLocator();

  // تهيئة الخدمات
  await serviceLocator<CacheService>().initialize();
  await serviceLocator<LoggingService>().initialize();

  // تهيئة أدوات الأداء والذاكرة
  ErrorHandler().initialize();
  PerformanceMonitor().initialize();
  MemoryManager().initialize();

  serviceLocator<LoggingService>()
      .logInfo('All services initialized successfully');

  // تهيئة مجلدات التطبيق
  try {
    final appDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${appDir.path}/backups');
    final exportDir = Directory('${appDir.path}/exports');

    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    if (!await exportDir.exists()) {
      await exportDir.create(recursive: true);
    }

    serviceLocator<LoggingService>()
        .logInfo('Application directories initialized successfully');
    serviceLocator<LoggingService>()
        .logInfo('Backup directory: ${backupDir.path}');
    serviceLocator<LoggingService>()
        .logInfo('Export directory: ${exportDir.path}');
  } catch (e) {
    serviceLocator<LoggingService>()
        .logError('Failed to initialize application directories', error: e);
  }

  // اختبار قاعدة البيانات
  serviceLocator<LoggingService>().logInfo('=== Database Testing ===');
  final authService = AuthService();

  try {
    // إنشاء المستخدم الافتراضي
    serviceLocator<LoggingService>().logInfo('Creating default user...');
    await authService.createDefaultUser();
    serviceLocator<LoggingService>()
        .logInfo('Default user created successfully');

    // التحقق من وجود المستخدم
    serviceLocator<LoggingService>().logInfo('Checking for existing users...');
    final hasUsers = await authService.hasUsers();
    if (hasUsers) {
      serviceLocator<LoggingService>().logInfo('Users exist in the system');

      // اختبار البحث عن المستخدم
      final user = await authService.login('admin', 'admin123');
      if (user != null) {
        serviceLocator<LoggingService>().logInfo('Login test successful');
        serviceLocator<LoggingService>().logInfo(
            'User info: ID=${user.id}, Name=${user.name}, Username=${user.username}, Role=${user.role}, Active=${user.isActive}');
      } else {
        serviceLocator<LoggingService>().logError('Login test failed');
      }
    } else {
      serviceLocator<LoggingService>().logError('No users found in the system');
    }

    // إنشاء بيانات تجريبية
    serviceLocator<LoggingService>().logInfo('Creating sample data...');
    final dbService = DatabaseService();
    await dbService.createSampleData();
    serviceLocator<LoggingService>()
        .logInfo('Sample data created successfully');
  } catch (e) {
    serviceLocator<LoggingService>().logError('Database test failed', error: e);
  }

  runApp(const InventoryApp());
}

class InventoryApp extends StatefulWidget {
  const InventoryApp({Key? key}) : super(key: key);

  @override
  State<InventoryApp> createState() => _InventoryAppState();
}

class _InventoryAppState extends State<InventoryApp> {
  Locale _locale = const Locale('ar');

  @override
  void initState() {
    super.initState();
  }

  void setLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppTexts.appTitle,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        brightness: Brightness.light,
        primaryColor: AppColors.primary,
        scaffoldBackgroundColor: AppColors.background,

        // ===== أزرار مرتفعة (Elevated Buttons) =====
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.buttonPrimary,
            foregroundColor: AppColors.textInverse,
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16), // مستديرة
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // ===== أزرار محيطية (Outlined Buttons) =====
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.buttonSecondary,
            side:
                const BorderSide(color: AppColors.buttonSecondary, width: 1.5),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // ===== أزرار نصية (Text Buttons) =====
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            textStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        // ===== حقول الإدخال (Input Fields) =====
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.surface,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: AppColors.cardBorder, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: AppColors.cardBorder, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: AppColors.error, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(color: AppColors.error, width: 2),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          labelStyle: const TextStyle(
            color: AppColors.textLight,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          hintStyle: TextStyle(
            color: AppColors.textLight.withValues(alpha: 0.7),
            fontSize: 14,
          ),
        ),

        // ===== شريط التطبيق (App Bar) =====
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.surface,
          foregroundColor: AppColors.textDark,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: GoogleFonts.ptSans(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
          iconTheme: const IconThemeData(color: AppColors.textDark),
        ),

        // ===== الخطوط (Typography) =====
        textTheme:
            GoogleFonts.ptSansTextTheme(Theme.of(context).textTheme).copyWith(
          // العناوين الرئيسية (20-28px)
          headlineLarge: GoogleFonts.ptSans(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
          headlineMedium: GoogleFonts.ptSans(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
          headlineSmall: GoogleFonts.ptSans(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),

          // العناوين الفرعية (16-20px)
          titleLarge: GoogleFonts.ptSans(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
          titleMedium: GoogleFonts.ptSans(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
          titleSmall: GoogleFonts.ptSans(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.textDark,
          ),

          // النص الأساسي (14-16px)
          bodyLarge: GoogleFonts.ptSans(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: AppColors.textDark,
          ),
          bodyMedium: GoogleFonts.ptSans(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: AppColors.textDark,
          ),

          // النص الثانوي (12-14px)
          bodySmall: GoogleFonts.ptSans(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: AppColors.textLight,
          ),
          labelLarge: GoogleFonts.ptSans(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textLight,
          ),
        ),

        // ===== ألوان إضافية =====
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppColors.primary,
          brightness: Brightness.light,
        ).copyWith(
          primary: AppColors.primary,
          secondary: AppColors.secondary,
          surface: AppColors.surface,
          error: AppColors.error,
          onPrimary: AppColors.textInverse,
          onSecondary: AppColors.textInverse,
          onSurface: AppColors.textDark,
          onError: AppColors.textInverse,
        ),
      ),

      // ===== دعم اللغات =====
      locale: _locale,
      supportedLocales: const [Locale('ar'), Locale('en')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      localeResolutionCallback: (locale, supportedLocales) {
        if (locale == null) return supportedLocales.first;
        for (var supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale.languageCode) {
            return supportedLocale;
          }
        }
        return supportedLocales.first;
      },

      // ===== دعم RTL =====
      builder: (context, child) {
        return Directionality(
          textDirection: _locale.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },

      // ===== الصفحة الرئيسية والروابط =====
      home: LoginView(onLocaleChange: setLocale),
      routes: {
        '/login': (context) => LoginView(onLocaleChange: setLocale),
        '/dashboard': (context) => const OptimizedDashboardView(),
        '/items': (context) => const OptimizedItemListView(),
        '/transactions': (context) => const TransactionListView(),
        '/reports': (context) => const ReportsView(),
        '/users': (context) => const UsersView(),
        '/settings': (context) => const SettingsView(),
      },
    );
  }
}
