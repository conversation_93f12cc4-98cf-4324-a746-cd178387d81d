import 'dart:developer' as developer;
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class LoggingService {
  static const String _logFileName = 'app_logs.txt';
  File? _logFile;
  
  Future<void> initialize() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      _logFile = File('${directory.path}/$_logFileName');
      
      // Create log file if it doesn't exist
      if (!await _logFile!.exists()) {
        await _logFile!.create();
      }
    } catch (e) {
      developer.log('Failed to initialize logging service: $e');
    }
  }

  void logInfo(String message, {String? tag}) {
    _log('INFO', message, tag: tag);
  }

  void logWarning(String message, {String? tag}) {
    _log('WARNING', message, tag: tag);
  }

  void logError(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log('ERROR', message, tag: tag, error: error, stackTrace: stackTrace);
  }

  void logDebug(String message, {String? tag}) {
    _log('DEBUG', message, tag: tag);
  }

  void logPerformance(String operation, Duration duration, {String? tag}) {
    _log('PERFORMANCE', '$operation took ${duration.inMilliseconds}ms', tag: tag);
  }

  void logDatabaseQuery(String query, Duration duration) {
    _log('DATABASE', 'Query: $query (${duration.inMilliseconds}ms)');
  }

  void logCacheHit(String key) {
    _log('CACHE', 'Cache HIT: $key');
  }

  void logCacheMiss(String key) {
    _log('CACHE', 'Cache MISS: $key');
  }

  void _log(String level, String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    final timestamp = DateTime.now().toIso8601String();
    final tagStr = tag != null ? '[$tag] ' : '';
    final logMessage = '$timestamp [$level] $tagStr$message';
    
    // Log to console
    developer.log(logMessage);
    
    // Log to file
    _writeToFile(logMessage, error: error, stackTrace: stackTrace);
  }

  Future<void> _writeToFile(String message, {Object? error, StackTrace? stackTrace}) async {
    if (_logFile == null) return;
    
    try {
      final buffer = StringBuffer(message);
      
      if (error != null) {
        buffer.writeln('\nError: $error');
      }
      
      if (stackTrace != null) {
        buffer.writeln('Stack trace:\n$stackTrace');
      }
      
      buffer.writeln();
      
      await _logFile!.writeAsString(buffer.toString(), mode: FileMode.append);
    } catch (e) {
      developer.log('Failed to write to log file: $e');
    }
  }

  Future<String> getLogs() async {
    if (_logFile == null || !await _logFile!.exists()) {
      return 'No logs available';
    }
    
    try {
      return await _logFile!.readAsString();
    } catch (e) {
      return 'Error reading logs: $e';
    }
  }

  Future<void> clearLogs() async {
    if (_logFile == null) return;
    
    try {
      await _logFile!.writeAsString('');
    } catch (e) {
      developer.log('Failed to clear logs: $e');
    }
  }

  Future<void> rotateLogs() async {
    if (_logFile == null || !await _logFile!.exists()) return;
    
    try {
      final stat = await _logFile!.stat();
      const maxSize = 5 * 1024 * 1024; // 5MB
      
      if (stat.size > maxSize) {
        final directory = _logFile!.parent;
        final backupFile = File('${directory.path}/${_logFileName}.backup');
        
        // Move current log to backup
        await _logFile!.copy(backupFile.path);
        
        // Clear current log
        await _logFile!.writeAsString('');
        
        logInfo('Log file rotated due to size limit');
      }
    } catch (e) {
      developer.log('Failed to rotate logs: $e');
    }
  }
}
