import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';

enum InputVariant {
  outlined,
  filled,
  underlined,
}

class ModernInput extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconTap;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onTap;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final InputVariant variant;
  final bool isRequired;
  final FocusNode? focusNode;
  final String? Function(String?)? validator;

  const ModernInput({
    Key? key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.controller,
    this.onChanged,
    this.onTap,
    this.keyboardType,
    this.inputFormatters,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.variant = InputVariant.outlined,
    this.isRequired = false,
    this.focusNode,
    this.validator,
  }) : super(key: key);

  @override
  State<ModernInput> createState() => _ModernInputState();
}

class _ModernInputState extends State<ModernInput>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _borderColorAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _animationController = AnimationController(
      duration: AppSpacing.animationFast,
      vsync: this,
    );
    _borderColorAnimation = ColorTween(
      begin: AppColors.outline,
      end: AppColors.primary,
    ).animate(_animationController);

    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ModernInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    _hasError = widget.errorText != null;
    if (_hasError) {
      _animationController.reset();
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (_isFocused && !_hasError) {
      _animationController.forward();
    } else if (!_hasError) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    _hasError = widget.errorText != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          _buildLabel(context, isArabic),
          AppSpacing.verticalSpaceXs,
        ],
        AnimatedBuilder(
          animation: _borderColorAnimation,
          builder: (context, child) {
            return TextFormField(
              controller: widget.controller,
              focusNode: _focusNode,
              onChanged: widget.onChanged,
              onTap: widget.onTap,
              keyboardType: widget.keyboardType,
              inputFormatters: widget.inputFormatters,
              obscureText: widget.obscureText,
              enabled: widget.enabled,
              readOnly: widget.readOnly,
              maxLines: widget.maxLines,
              maxLength: widget.maxLength,
              validator: widget.validator,
              style: AppTypography.bodyLarge(context, isArabic: isArabic),
              decoration: _buildInputDecoration(context, isArabic),
            );
          },
        ),
        if (widget.helperText != null || widget.errorText != null) ...[
          AppSpacing.verticalSpaceXs,
          _buildHelperText(context, isArabic),
        ],
      ],
    );
  }

  Widget _buildLabel(BuildContext context, bool isArabic) {
    return RichText(
      text: TextSpan(
        text: widget.label,
        style: AppTypography.labelLarge(context, isArabic: isArabic).copyWith(
          color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
        children: [
          if (widget.isRequired)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHelperText(BuildContext context, bool isArabic) {
    final text = widget.errorText ?? widget.helperText;
    final color = widget.errorText != null ? AppColors.error : AppColors.onSurfaceVariant;

    return Row(
      children: [
        if (widget.errorText != null) ...[
          Icon(
            Icons.error_outline,
            size: AppSpacing.iconSm,
            color: AppColors.error,
          ),
          AppSpacing.horizontalSpaceXs,
        ],
        Expanded(
          child: Text(
            text!,
            style: AppTypography.bodySmall(context, isArabic: isArabic).copyWith(
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  InputDecoration _buildInputDecoration(BuildContext context, bool isArabic) {
    final borderColor = _hasError 
        ? AppColors.error 
        : (_isFocused ? _borderColorAnimation.value : AppColors.outline);

    switch (widget.variant) {
      case InputVariant.outlined:
        return InputDecoration(
          hintText: widget.hint,
          hintStyle: AppTypography.bodyLarge(context, isArabic: isArabic).copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          prefixIcon: widget.prefixIcon != null
              ? Icon(
                  widget.prefixIcon,
                  color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
                )
              : null,
          suffixIcon: widget.suffixIcon != null
              ? IconButton(
                  icon: Icon(
                    widget.suffixIcon,
                    color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
                  ),
                  onPressed: widget.onSuffixIconTap,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: AppColors.outline),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: AppColors.outline),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: borderColor!, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: AppColors.error),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: AppColors.error, width: 2),
          ),
          filled: false,
          contentPadding: AppSpacing.paddingMd,
        );

      case InputVariant.filled:
        return InputDecoration(
          hintText: widget.hint,
          hintStyle: AppTypography.bodyLarge(context, isArabic: isArabic).copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          prefixIcon: widget.prefixIcon != null
              ? Icon(
                  widget.prefixIcon,
                  color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
                )
              : null,
          suffixIcon: widget.suffixIcon != null
              ? IconButton(
                  icon: Icon(
                    widget.suffixIcon,
                    color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
                  ),
                  onPressed: widget.onSuffixIconTap,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: borderColor!, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: AppColors.error),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: AppSpacing.borderRadiusMd,
            borderSide: BorderSide(color: AppColors.error, width: 2),
          ),
          filled: true,
          fillColor: AppColors.surfaceVariant,
          contentPadding: AppSpacing.paddingMd,
        );

      case InputVariant.underlined:
        return InputDecoration(
          hintText: widget.hint,
          hintStyle: AppTypography.bodyLarge(context, isArabic: isArabic).copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          prefixIcon: widget.prefixIcon != null
              ? Icon(
                  widget.prefixIcon,
                  color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
                )
              : null,
          suffixIcon: widget.suffixIcon != null
              ? IconButton(
                  icon: Icon(
                    widget.suffixIcon,
                    color: _hasError ? AppColors.error : AppColors.onSurfaceVariant,
                  ),
                  onPressed: widget.onSuffixIconTap,
                )
              : null,
          border: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.outline),
          ),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.outline),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: borderColor!, width: 2),
          ),
          errorBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.error),
          ),
          focusedErrorBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.error, width: 2),
          ),
          filled: false,
          contentPadding: AppSpacing.paddingVerticalMd,
        );
    }
  }
}

// Specialized input components
class SearchInput extends StatelessWidget {
  final String? hint;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;

  const SearchInput({
    Key? key,
    this.hint,
    this.controller,
    this.onChanged,
    this.onClear,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return ModernInput(
      hint: hint ?? (isArabic ? 'البحث...' : 'Search...'),
      controller: controller,
      onChanged: onChanged,
      prefixIcon: Icons.search,
      suffixIcon: controller?.text.isNotEmpty == true ? Icons.clear : null,
      onSuffixIconTap: onClear,
      variant: InputVariant.filled,
    );
  }
}

class PasswordInput extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final bool isRequired;

  const PasswordInput({
    Key? key,
    this.label,
    this.hint,
    this.controller,
    this.onChanged,
    this.validator,
    this.isRequired = false,
  }) : super(key: key);

  @override
  State<PasswordInput> createState() => _PasswordInputState();
}

class _PasswordInputState extends State<PasswordInput> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return ModernInput(
      label: widget.label,
      hint: widget.hint ?? (isArabic ? 'كلمة المرور' : 'Password'),
      controller: widget.controller,
      onChanged: widget.onChanged,
      validator: widget.validator,
      isRequired: widget.isRequired,
      obscureText: _obscureText,
      prefixIcon: Icons.lock_outline,
      suffixIcon: _obscureText ? Icons.visibility : Icons.visibility_off,
      onSuffixIconTap: () {
        setState(() {
          _obscureText = !_obscureText;
        });
      },
    );
  }
}
