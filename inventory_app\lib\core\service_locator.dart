import 'package:get_it/get_it.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';
import '../services/cache_service.dart';
import '../services/logging_service.dart';
import '../repositories/item_repository.dart';
import '../repositories/transaction_repository.dart';
import '../repositories/user_repository.dart';
import '../repositories/dashboard_repository.dart';

final GetIt serviceLocator = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Core Services
  serviceLocator.registerLazySingleton<LoggingService>(() => LoggingService());
  serviceLocator.registerLazySingleton<CacheService>(() => CacheService());
  serviceLocator.registerLazySingleton<DatabaseService>(() => DatabaseService());
  serviceLocator.registerLazySingleton<AuthService>(() => AuthService());

  // Repositories
  serviceLocator.registerLazySingleton<ItemRepository>(
    () => ItemRepository(
      serviceLocator<DatabaseService>(),
      serviceLocator<CacheService>(),
      serviceLocator<LoggingService>(),
    ),
  );

  serviceLocator.registerLazySingleton<TransactionRepository>(
    () => TransactionRepository(
      serviceLocator<DatabaseService>(),
      serviceLocator<CacheService>(),
      serviceLocator<LoggingService>(),
    ),
  );

  serviceLocator.registerLazySingleton<UserRepository>(
    () => UserRepository(
      serviceLocator<DatabaseService>(),
      serviceLocator<CacheService>(),
      serviceLocator<LoggingService>(),
    ),
  );

  serviceLocator.registerLazySingleton<DashboardRepository>(
    () => DashboardRepository(
      serviceLocator<DatabaseService>(),
      serviceLocator<CacheService>(),
      serviceLocator<LoggingService>(),
    ),
  );
}
