class AppTexts {
  // العناوين الرئيسية
  static const String appTitle = 'نظام إدارة المخزن';
  static const String appTitleEn = 'Inventory Management System';
  static const String dashboard = 'لوحة التحكم';
  static const String dashboardEn = 'Dashboard';
  static const String inventory = 'المخزون';
  static const String inventoryEn = 'Inventory';
  static const String transactions = 'العمليات';
  static const String transactionsEn = 'Transactions';
  static const String reports = 'التقارير';
  static const String reportsEn = 'Reports';
  static const String users = 'المستخدمين';
  static const String usersEn = 'Users';
  static const String settings = 'الإعدادات';
  static const String settingsEn = 'Settings';

  // لوحة التحكم
  static const String totalItems = 'إجمالي الأصناف';
  static const String totalItemsEn = 'Total Items';
  static const String totalQuantity = 'إجمالي الكميات';
  static const String totalQuantityEn = 'Total Quantity';
  static const String lowStockItems = 'أصناف منخفضة المخزون';
  static const String lowStockItemsEn = 'Low Stock Items';
  static const String categories = 'الفئات';
  static const String categoriesEn = 'Categories';
  static const String recentTransactions = 'آخر الحركات';
  static const String recentTransactionsEn = 'Recent Transactions';
  static const String stockStatus = 'حالة المخزون';
  static const String stockStatusEn = 'Stock Status';

  // إدارة الأصناف
  static const String addItem = 'إضافة صنف';
  static const String addItemEn = 'Add Item';
  static const String editItem = 'تعديل الصنف';
  static const String editItemEn = 'Edit Item';
  static const String deleteItem = 'حذف الصنف';
  static const String deleteItemEn = 'Delete Item';
  static const String itemName = 'اسم الصنف';
  static const String itemNameEn = 'Item Name';
  static const String itemCode = 'كود الصنف';
  static const String itemCodeEn = 'Item Code';
  static const String category = 'الفئة';
  static const String categoryEn = 'Category';
  static const String quantity = 'الكمية';
  static const String quantityEn = 'Quantity';
  static const String unit = 'الوحدة';
  static const String unitEn = 'Unit';
  static const String minQuantity = 'الحد الأدنى';
  static const String minQuantityEn = 'Minimum Quantity';
  static const String description = 'الوصف';
  static const String descriptionEn = 'Description';
  static const String location = 'الموقع';
  static const String locationEn = 'Location';
  static const String searchItems = 'البحث في الأصناف...';
  static const String searchItemsEn = 'Search items...';

  // إدارة الحركات
  static const String addTransaction = 'إضافة عملية';
  static const String addTransactionEn = 'Add Transaction';
  static const String transactionType = 'نوع الحركة';
  static const String transactionTypeEn = 'Transaction Type';
  static const String incoming = 'وارد';
  static const String incomingEn = 'Incoming';
  static const String outgoing = 'صادر';
  static const String outgoingEn = 'Outgoing';
  static const String consumption = 'استهلاك';
  static const String consumptionEn = 'Consumption';
  static const String adjustment = 'تعديل';
  static const String adjustmentEn = 'Adjustment';
  static const String workOrder = 'أمر العمل';
  static const String workOrderEn = 'Work Order';
  static const String requestForm = 'كشف الطلب';
  static const String requestFormEn = 'Request Form';
  static const String responsiblePerson = 'الشخص المسؤول';
  static const String responsiblePersonEn = 'Responsible Person';
  static const String notes = 'ملاحظات';
  static const String notesEn = 'Notes';
  static const String date = 'التاريخ';
  static const String dateEn = 'Date';

  // التقارير
  static const String currentInventory = 'المخزون الحالي';
  static const String currentInventoryEn = 'Current Inventory';
  static const String lowStockReport = 'تقرير المخزون المنخفض';
  static const String lowStockReportEn = 'Low Stock Report';
  static const String transactionSummary = 'ملخص الحركات';
  static const String transactionSummaryEn = 'Transaction Summary';
  static const String exportToCSV = 'تصدير إلى CSV';
  static const String exportToCSVEn = 'Export to CSV';
  static const String filterByDate = 'تصفية حسب التاريخ';
  static const String filterByDateEn = 'Filter by Date';
  static const String filterByCategory = 'تصفية حسب الفئة';
  static const String filterByCategoryEn = 'Filter by Category';

  // إدارة المستخدمين
  static const String addUser = 'إضافة مستخدم';
  static const String addUserEn = 'Add User';
  static const String editUser = 'تعديل المستخدم';
  static const String editUserEn = 'Edit User';
  static const String deleteUser = 'حذف المستخدم';
  static const String deleteUserEn = 'Delete User';
  static const String userName = 'اسم المستخدم';
  static const String userNameEn = 'User Name';
  static const String username = 'اسم المستخدم';
  static const String usernameEn = 'Username';
  static const String password = 'كلمة المرور';
  static const String passwordEn = 'Password';
  static const String role = 'الدور';
  static const String roleEn = 'Role';
  static const String admin = 'مدير';
  static const String adminEn = 'Admin';
  static const String manager = 'مشرف';
  static const String managerEn = 'Manager';
  static const String user = 'مستخدم';
  static const String userEn = 'User';

  // الأزرار
  static const String save = 'حفظ';
  static const String saveEn = 'Save';
  static const String cancel = 'إلغاء';
  static const String cancelEn = 'Cancel';
  static const String delete = 'حذف';
  static const String deleteEn = 'Delete';
  static const String edit = 'تعديل';
  static const String editEn = 'Edit';
  static const String add = 'إضافة';
  static const String addEn = 'Add';
  static const String search = 'بحث';
  static const String searchEn = 'Search';
  static const String filter = 'تصفية';
  static const String filterEn = 'Filter';
  static const String export = 'تصدير';
  static const String exportEn = 'Export';
  static const String import = 'استيراد';
  static const String importEn = 'Import';
  static const String login = 'تسجيل الدخول';
  static const String loginEn = 'Login';
  static const String logout = 'تسجيل الخروج';
  static const String logoutEn = 'Logout';

  // الرسائل
  static const String confirmDelete = 'تأكيد الحذف';
  static const String confirmDeleteEn = 'Are you sure you want to delete?';
  static const String itemAdded = 'تم إضافة الصنف بنجاح';
  static const String itemAddedEn = 'Item added successfully';
  static const String itemUpdated = 'تم تحديث الصنف بنجاح';
  static const String itemUpdatedEn = 'Item updated successfully';
  static const String itemDeleted = 'تم حذف الصنف بنجاح';
  static const String itemDeletedEn = 'Item deleted successfully';
  static const String transactionAdded = 'تم إضافة الحركة بنجاح';
  static const String transactionAddedEn = 'Transaction added successfully';
  static const String userAdded = 'تم إضافة المستخدم بنجاح';
  static const String userAddedEn = 'User added successfully';
  static const String userUpdated = 'تم تحديث المستخدم بنجاح';
  static const String userUpdatedEn = 'User updated successfully';
  static const String userDeleted = 'تم حذف المستخدم بنجاح';
  static const String userDeletedEn = 'User deleted successfully';
  static const String exportSuccess = 'تم التصدير بنجاح';
  static const String exportSuccessEn = 'Export completed successfully';
  static const String noData = 'لا توجد بيانات';
  static const String noDataEn = 'No data available';
  static const String loading = 'جاري التحميل...';
  static const String loadingEn = 'Loading...';
  static const String error = 'حدث خطأ';
  static const String errorEn = 'An error occurred';
  static const String success = 'تم بنجاح';
  static const String successEn = 'Success';
  static const String warning = 'تحذير';
  static const String warningEn = 'Warning';
  static const String info = 'معلومات';
  static const String infoEn = 'Information';

  // الحقول المطلوبة
  static const String requiredField = 'هذا الحقل مطلوب';
  static const String requiredFieldEn = 'This field is required';
  static const String invalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String invalidEmailEn = 'Invalid email address';
  static const String invalidQuantity = 'الكمية غير صحيحة';
  static const String invalidQuantityEn = 'Invalid quantity';
  static const String invalidDate = 'التاريخ غير صحيح';
  static const String invalidDateEn = 'Invalid date';

  // Navigation
  static const String items = 'الأصناف';
  static const String viewReports = 'عرض التقارير';

  // Auth
  static const String loginButton = 'دخول';
  static const String loginError = 'خطأ في تسجيل الدخول';
  static const String invalidCredentials = 'بيانات غير صحيحة';

  // Dashboard
  static const String quickActions = 'إجراءات سريعة';

  // Items
  static const String status = 'الحالة';
  static const String inStock = 'متوفر';
  static const String lowStock = 'مخزون منخفض';
  static const String outOfStock = 'نفذ المخزون';
  static const String filterItems = 'تصفية الأصناف';
  static const String addNewItem = 'إضافة صنف جديد';
  static const String editTransaction = 'تعديل العملية';
  static const String deleteTransaction = 'حذف العملية';

  // Transactions
  static const String transactionDate = 'تاريخ العملية';

  // Reports
  static const String overview = 'نظرة عامة';
  static const String analytics = 'التحليلات';
  static const String refresh = 'تحديث';
  static const String allTypes = 'جميع الأنواع';
  static const String filterByType = 'تصفية حسب النوع';
  static const String startDate = 'تاريخ البداية';
  static const String endDate = 'تاريخ النهاية';
  static const String apply = 'تطبيق';

  // Report Titles
  static const String itemsByCategory = 'الأصناف حسب الفئة';
  static const String monthlyTransactions = 'العمليات الشهرية';
  static const String mostActiveItems = 'الأصناف الأكثر نشاطاً';
  static const String transactionTrends = 'اتجاهات العمليات';
  static const String recentActivity = 'النشاط الحديث';
  static const String performanceMetrics = 'مقاييس الأداء';

  // Export
  static const String exportToExcel = 'تصدير إلى Excel';
  static const String exportItems = 'تصدير الأصناف';
  static const String exportTransactions = 'تصدير العمليات';
  static const String exportError = 'خطأ في التصدير';
  static const String fileSaved = 'تم حفظ الملف';

  // Chart Labels
  static const String type = 'النوع';

  // Status Messages
  static const String errorLoadingData = 'خطأ في تحميل البيانات';
  static const String deleteConfirmation = 'هل أنت متأكد من حذف هذا العنصر؟';
  static const String yes = 'نعم';
  static const String no = 'لا';

  // Units
  static const String meter = 'متر';
  static const String piece = 'قطعة';
  static const String box = 'صندوق';
  static const String roll = 'بكرة';
  static const String kg = 'كجم';
  static const String liter = 'لتر';

  // Categories
  static const String copperCable = 'كابل نحاس';
  static const String fiberCable = 'كابل فايبر';
  static const String copperWelding = 'لحام نحاس';
  static const String fiberWelding = 'لحام فايبر';
  static const String other = 'أخرى';
}
