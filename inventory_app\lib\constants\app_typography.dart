import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTypography {
  // Base font family
  static String get fontFamily => GoogleFonts.inter().fontFamily!;
  static String get arabicFontFamily => GoogleFonts.cairo().fontFamily!;
  
  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  
  // Material Design 3 Typography Scale
  
  // Display styles - for large, impactful text
  static TextStyle displayLarge(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 57,
      fontWeight: regular,
      height: 1.12,
      letterSpacing: -0.25,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle displayMedium(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 45,
      fontWeight: regular,
      height: 1.16,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle displaySmall(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 36,
      fontWeight: regular,
      height: 1.22,
      color: AppColors.onSurface,
    );
  }
  
  // Headline styles - for section headers
  static TextStyle headlineLarge(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 32,
      fontWeight: regular,
      height: 1.25,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle headlineMedium(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 28,
      fontWeight: regular,
      height: 1.29,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle headlineSmall(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 24,
      fontWeight: regular,
      height: 1.33,
      color: AppColors.onSurface,
    );
  }
  
  // Title styles - for card headers and prominent text
  static TextStyle titleLarge(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 22,
      fontWeight: regular,
      height: 1.27,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle titleMedium(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 16,
      fontWeight: medium,
      height: 1.50,
      letterSpacing: 0.15,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle titleSmall(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 14,
      fontWeight: medium,
      height: 1.43,
      letterSpacing: 0.1,
      color: AppColors.onSurface,
    );
  }
  
  // Label styles - for buttons and small text
  static TextStyle labelLarge(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 14,
      fontWeight: medium,
      height: 1.43,
      letterSpacing: 0.1,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle labelMedium(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 12,
      fontWeight: medium,
      height: 1.33,
      letterSpacing: 0.5,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle labelSmall(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 11,
      fontWeight: medium,
      height: 1.45,
      letterSpacing: 0.5,
      color: AppColors.onSurface,
    );
  }
  
  // Body styles - for main content
  static TextStyle bodyLarge(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 16,
      fontWeight: regular,
      height: 1.50,
      letterSpacing: 0.15,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle bodyMedium(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 14,
      fontWeight: regular,
      height: 1.43,
      letterSpacing: 0.25,
      color: AppColors.onSurface,
    );
  }
  
  static TextStyle bodySmall(BuildContext context, {bool isArabic = false}) {
    return GoogleFonts.getFont(
      isArabic ? 'Cairo' : 'Inter',
      fontSize: 12,
      fontWeight: regular,
      height: 1.33,
      letterSpacing: 0.4,
      color: AppColors.onSurface,
    );
  }
  
  // Specialized styles for the inventory app
  
  // Dashboard styles
  static TextStyle dashboardTitle(BuildContext context, {bool isArabic = false}) {
    return headlineMedium(context, isArabic: isArabic).copyWith(
      fontWeight: semiBold,
      color: AppColors.primary,
    );
  }
  
  static TextStyle cardTitle(BuildContext context, {bool isArabic = false}) {
    return titleMedium(context, isArabic: isArabic).copyWith(
      fontWeight: semiBold,
    );
  }
  
  static TextStyle cardSubtitle(BuildContext context, {bool isArabic = false}) {
    return bodyMedium(context, isArabic: isArabic).copyWith(
      color: AppColors.onSurfaceVariant,
    );
  }
  
  // Button styles
  static TextStyle buttonLarge(BuildContext context, {bool isArabic = false}) {
    return labelLarge(context, isArabic: isArabic).copyWith(
      fontWeight: semiBold,
    );
  }
  
  static TextStyle buttonMedium(BuildContext context, {bool isArabic = false}) {
    return labelMedium(context, isArabic: isArabic).copyWith(
      fontWeight: semiBold,
    );
  }
  
  // Form styles
  static TextStyle inputLabel(BuildContext context, {bool isArabic = false}) {
    return bodyMedium(context, isArabic: isArabic).copyWith(
      fontWeight: medium,
      color: AppColors.onSurfaceVariant,
    );
  }
  
  static TextStyle inputText(BuildContext context, {bool isArabic = false}) {
    return bodyLarge(context, isArabic: isArabic);
  }
  
  static TextStyle inputHint(BuildContext context, {bool isArabic = false}) {
    return bodyLarge(context, isArabic: isArabic).copyWith(
      color: AppColors.onSurfaceVariant,
    );
  }
  
  static TextStyle inputError(BuildContext context, {bool isArabic = false}) {
    return bodySmall(context, isArabic: isArabic).copyWith(
      color: AppColors.error,
    );
  }
  
  // Status styles
  static TextStyle statusSuccess(BuildContext context, {bool isArabic = false}) {
    return labelMedium(context, isArabic: isArabic).copyWith(
      color: AppColors.success,
      fontWeight: semiBold,
    );
  }
  
  static TextStyle statusWarning(BuildContext context, {bool isArabic = false}) {
    return labelMedium(context, isArabic: isArabic).copyWith(
      color: AppColors.warning,
      fontWeight: semiBold,
    );
  }
  
  static TextStyle statusError(BuildContext context, {bool isArabic = false}) {
    return labelMedium(context, isArabic: isArabic).copyWith(
      color: AppColors.error,
      fontWeight: semiBold,
    );
  }
  
  // Number styles for inventory
  static TextStyle quantityLarge(BuildContext context, {bool isArabic = false}) {
    return headlineSmall(context, isArabic: isArabic).copyWith(
      fontWeight: bold,
      color: AppColors.primary,
    );
  }
  
  static TextStyle quantityMedium(BuildContext context, {bool isArabic = false}) {
    return titleLarge(context, isArabic: isArabic).copyWith(
      fontWeight: semiBold,
    );
  }
  
  static TextStyle quantitySmall(BuildContext context, {bool isArabic = false}) {
    return titleMedium(context, isArabic: isArabic).copyWith(
      fontWeight: medium,
    );
  }
  
  // Helper method to get text direction
  static TextDirection getTextDirection(bool isArabic) {
    return isArabic ? TextDirection.rtl : TextDirection.ltr;
  }
  
  // Helper method to get appropriate text style based on locale
  static TextStyle getLocalizedStyle(
    BuildContext context,
    TextStyle Function(BuildContext, {bool isArabic}) styleFunction,
  ) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    return styleFunction(context, isArabic: isArabic);
  }
}
