import 'package:flutter/material.dart';

class AppColors {
  // ===== الألوان الأساسية (Core Colors) - Material Design 3 =====
  static const Color primary = Color(0xFF1565C0); // Modern Blue
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color primaryContainer = Color(0xFFD1E4FF);
  static const Color onPrimaryContainer = Color(0xFF001D36);
  static const Color primaryLight = primaryContainer; // Backward compatibility
  static const Color primaryDark = Color(0xFF0D47A1);

  static const Color secondary = Color(0xFF00695C); // Teal
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color secondaryContainer = Color(0xFFB2DFDB);
  static const Color onSecondaryContainer = Color(0xFF002019);

  static const Color tertiary = Color(0xFFE65100); // Orange accent
  static const Color onTertiary = Color(0xFFFFFFFF);
  static const Color tertiaryContainer = Color(0xFFFFDCC1);
  static const Color onTertiaryContainer = Color(0xFF2D1600);
  static const Color accent = tertiary; // Backward compatibility

  static const Color error = Color(0xFFBA1A1A);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color errorContainer = Color(0xFFFFDAD6);
  static const Color onErrorContainer = Color(0xFF410002);

  // ===== ألوان النصوص (Text Colors) =====
  static const Color textDark = Color(0xFF2E2E2E); // Charcoal
  static const Color textLight = Color(0xFF757575); // Gray
  static const Color textInverse = Color(0xFFFFFFFF); // White

  // ===== ألوان الخلفية (Background Colors) - Material Design 3 =====
  static const Color background = Color(0xFFFEFBFF);
  static const Color onBackground = Color(0xFF1C1B1F);
  static const Color surface = Color(0xFFFEFBFF);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color surfaceVariant = Color(0xFFE7E0EC);
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color surfaceTint = primary;

  // Enhanced surface colors
  static const Color card = surface;
  static const Color cardBorder = Color(0xFFCAC4D0);
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);

  // Inverse colors
  static const Color inverseSurface = Color(0xFF313033);
  static const Color onInverseSurface = Color(0xFFF4EFF4);
  static const Color inversePrimary = Color(0xFF9ECAFF);

  // ===== ألوان الحالة (Status Colors) - Enhanced =====
  static const Color success = Color(0xFF2E7D32);
  static const Color onSuccess = Color(0xFFFFFFFF);
  static const Color successContainer = Color(0xFFC8E6C9);
  static const Color onSuccessContainer = Color(0xFF1B5E20);
  static const Color successLight = successContainer; // Backward compatibility

  static const Color warning = Color(0xFFEF6C00);
  static const Color onWarning = Color(0xFFFFFFFF);
  static const Color warningContainer = Color(0xFFFFE0B2);
  static const Color onWarningContainer = Color(0xFFE65100);
  static const Color warningLight = warningContainer; // Backward compatibility

  static const Color errorLight = errorContainer; // Backward compatibility

  static const Color info = Color(0xFF0277BD);
  static const Color onInfo = Color(0xFFFFFFFF);
  static const Color infoContainer = Color(0xFFB3E5FC);
  static const Color onInfoContainer = Color(0xFF01579B);
  static const Color infoLight = infoContainer; // Backward compatibility

  // ===== ألوان المخزون (Inventory Status) =====
  static const Color stockNormal = Color(0xFF4CAF50); // Green
  static const Color stockLow = Color(0xFFFF9800); // Orange
  static const Color stockOut = Color(0xFFF44336); // Red

  // ===== ألوان الحركات (Transaction Types) =====
  static const Color incoming = Color(0xFF4CAF50); // Green
  static const Color outgoing = Color(0xFFF44336); // Red
  static const Color consumption = Color(0xFFFF9800); // Orange
  static const Color adjustment = Color(0xFF2196F3); // Blue

  // ===== ألوان الأزرار (Button Colors) =====
  static const Color buttonPrimary = Color(0xFF008080);
  static const Color buttonPrimaryHover = Color(0xFF006666);
  static const Color buttonSecondary = Color(0xFF70A4A4);
  static const Color buttonSecondaryHover = Color(0xFF5A8A8A);
  static const Color buttonSuccess = Color(0xFF4CAF50);
  static const Color buttonWarning = Color(0xFFFF9800);
  static const Color buttonError = Color(0xFFF44336);

  // ===== التدرجات اللونية (Gradients) =====
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, Color(0xFF5A8A8A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, Color(0xFFE6A700)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [success, Color(0xFF388E3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient warningGradient = LinearGradient(
    colors: [warning, Color(0xFFF57C00)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [error, Color(0xFFD32F2F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [surface, Color(0xFFF8FAFC)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [background, Color(0xFFD0F0F0)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // ===== الظلال (Shadows) =====
  static List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05),
      blurRadius: 10,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> elevatedShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      blurRadius: 15,
      offset: const Offset(0, 4),
    ),
  ];

  static List<BoxShadow> floatingShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.2),
      blurRadius: 20,
      offset: const Offset(0, 8),
    ),
  ];
}
