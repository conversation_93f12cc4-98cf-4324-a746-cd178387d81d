import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';
import 'modern_button.dart';

class EmptyState extends StatefulWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? actionText;
  final VoidCallback? onAction;
  final Widget? illustration;
  final Color? iconColor;
  final double? iconSize;

  const EmptyState({
    Key? key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onAction,
    this.illustration,
    this.iconColor,
    this.iconSize,
  }) : super(key: key);

  const EmptyState.noItems({
    Key? key,
    String? title,
    String? subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) : this(
          key: key,
          icon: Icons.inventory_2_outlined,
          title: title ?? 'No items found',
          subtitle: subtitle ?? 'Start by adding your first item to the inventory',
          actionText: actionText ?? 'Add Item',
          onAction: onAction,
          iconColor: AppColors.primary,
        );

  const EmptyState.noTransactions({
    Key? key,
    String? title,
    String? subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) : this(
          key: key,
          icon: Icons.receipt_long_outlined,
          title: title ?? 'No transactions found',
          subtitle: subtitle ?? 'Transaction history will appear here',
          actionText: actionText,
          onAction: onAction,
          iconColor: AppColors.secondary,
        );

  const EmptyState.noUsers({
    Key? key,
    String? title,
    String? subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) : this(
          key: key,
          icon: Icons.people_outline,
          title: title ?? 'No users found',
          subtitle: subtitle ?? 'Add team members to manage the inventory',
          actionText: actionText ?? 'Add User',
          onAction: onAction,
          iconColor: AppColors.tertiary,
        );

  const EmptyState.noData({
    Key? key,
    String? title,
    String? subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) : this(
          key: key,
          icon: Icons.data_usage_outlined,
          title: title ?? 'No data available',
          subtitle: subtitle ?? 'Data will appear here when available',
          actionText: actionText,
          onAction: onAction,
          iconColor: AppColors.onSurfaceVariant,
        );

  const EmptyState.searchResults({
    Key? key,
    String? title,
    String? subtitle,
  }) : this(
          key: key,
          icon: Icons.search_off_outlined,
          title: title ?? 'No results found',
          subtitle: subtitle ?? 'Try adjusting your search criteria',
          iconColor: AppColors.onSurfaceVariant,
        );

  @override
  State<EmptyState> createState() => _EmptyStateState();
}

class _EmptyStateState extends State<EmptyState>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Center(
          child: Padding(
            padding: AppSpacing.screenPaddingAll,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ScaleTransition(
                  scale: _scaleAnimation,
                  child: _buildIllustration(),
                ),
                AppSpacing.verticalSpaceLg,
                Text(
                  widget.title,
                  style: AppTypography.headlineSmall(context, isArabic: isArabic).copyWith(
                    color: AppColors.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (widget.subtitle != null) ...[
                  AppSpacing.verticalSpaceSm,
                  Text(
                    widget.subtitle!,
                    style: AppTypography.bodyLarge(context, isArabic: isArabic).copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                if (widget.actionText != null && widget.onAction != null) ...[
                  AppSpacing.verticalSpaceXl,
                  ModernButton.primary(
                    text: widget.actionText!,
                    onPressed: widget.onAction,
                    icon: Icons.add,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIllustration() {
    if (widget.illustration != null) {
      return widget.illustration!;
    }

    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: (widget.iconColor ?? AppColors.primary).withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        widget.icon,
        size: widget.iconSize ?? 64,
        color: widget.iconColor ?? AppColors.primary,
      ),
    );
  }
}

// Specialized empty states for different contexts
class EmptyInventory extends StatelessWidget {
  final VoidCallback? onAddItem;

  const EmptyInventory({
    Key? key,
    this.onAddItem,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return EmptyState(
      icon: Icons.inventory_2_outlined,
      title: isArabic ? 'لا توجد أصناف' : 'No Items Yet',
      subtitle: isArabic 
          ? 'ابدأ بإضافة أول صنف إلى المخزون'
          : 'Start building your inventory by adding your first item',
      actionText: isArabic ? 'إضافة صنف' : 'Add First Item',
      onAction: onAddItem,
      iconColor: AppColors.primary,
      illustration: _buildInventoryIllustration(),
    );
  }

  Widget _buildInventoryIllustration() {
    return Container(
      width: 160,
      height: 160,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.secondary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: AppSpacing.borderRadiusXl,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: 20,
            left: 20,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.2),
                borderRadius: AppSpacing.borderRadiusSm,
              ),
              child: Icon(
                Icons.inventory_2_outlined,
                color: AppColors.primary,
                size: 24,
              ),
            ),
          ),
          Positioned(
            bottom: 30,
            right: 30,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.secondary.withValues(alpha: 0.2),
                borderRadius: AppSpacing.borderRadiusSm,
              ),
              child: Icon(
                Icons.add,
                color: AppColors.secondary,
                size: 20,
              ),
            ),
          ),
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: AppColors.primary.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }
}

class EmptySearchResults extends StatelessWidget {
  final String searchTerm;
  final VoidCallback? onClearSearch;

  const EmptySearchResults({
    Key? key,
    required this.searchTerm,
    this.onClearSearch,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return EmptyState(
      icon: Icons.search_off_outlined,
      title: isArabic ? 'لا توجد نتائج' : 'No Results Found',
      subtitle: isArabic 
          ? 'لم نجد أي نتائج لـ "$searchTerm"'
          : 'We couldn\'t find any results for "$searchTerm"',
      actionText: isArabic ? 'مسح البحث' : 'Clear Search',
      onAction: onClearSearch,
      iconColor: AppColors.onSurfaceVariant,
    );
  }
}

class EmptyFilterResults extends StatelessWidget {
  final VoidCallback? onClearFilters;

  const EmptyFilterResults({
    Key? key,
    this.onClearFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return EmptyState(
      icon: Icons.filter_list_off_outlined,
      title: isArabic ? 'لا توجد نتائج' : 'No Matching Results',
      subtitle: isArabic 
          ? 'جرب تعديل المرشحات للعثور على ما تبحث عنه'
          : 'Try adjusting your filters to find what you\'re looking for',
      actionText: isArabic ? 'مسح المرشحات' : 'Clear Filters',
      onAction: onClearFilters,
      iconColor: AppColors.warning,
    );
  }
}
