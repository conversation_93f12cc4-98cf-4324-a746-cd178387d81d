import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';

enum ButtonVariant {
  primary,
  secondary,
  tertiary,
  success,
  warning,
  error,
  ghost,
}

enum ButtonSize {
  small,
  medium,
  large,
}

class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? child;

  const ModernButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.child,
  }) : super(key: key);

  const ModernButton.primary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    Widget? child,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          variant: ButtonVariant.primary,
          size: size,
          icon: icon,
          isLoading: isLoading,
          isFullWidth: isFullWidth,
          child: child,
        );

  const ModernButton.secondary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    Widget? child,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          variant: ButtonVariant.secondary,
          size: size,
          icon: icon,
          isLoading: isLoading,
          isFullWidth: isFullWidth,
          child: child,
        );

  const ModernButton.ghost({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    ButtonSize size = ButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
    Widget? child,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          variant: ButtonVariant.ghost,
          size: size,
          icon: icon,
          isLoading: isLoading,
          isFullWidth: isFullWidth,
          child: child,
        );

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppSpacing.animationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    final colors = _getColors();
    final padding = _getPadding();
    final textStyle = _getTextStyle(context, isArabic);

    Widget buttonChild = widget.child ?? _buildButtonContent(context, isArabic);

    if (widget.isLoading) {
      buttonChild = _buildLoadingContent(context, colors);
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.onPressed != null ? _onTapDown : null,
            onTapUp: widget.onPressed != null ? _onTapUp : null,
            onTapCancel: widget.onPressed != null ? _onTapCancel : null,
            child: Container(
              width: widget.isFullWidth ? double.maxFinite : null,
              decoration: BoxDecoration(
                color: colors.backgroundColor,
                borderRadius: AppSpacing.borderRadiusMd,
                border: colors.borderColor != null
                    ? Border.all(color: colors.borderColor!)
                    : null,
                boxShadow: widget.variant == ButtonVariant.primary ||
                        widget.variant == ButtonVariant.secondary
                    ? [
                        BoxShadow(
                          color: colors.backgroundColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.isLoading ? null : widget.onPressed,
                  borderRadius: AppSpacing.borderRadiusMd,
                  child: Container(
                    padding: padding,
                    child: buttonChild,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildButtonContent(BuildContext context, bool isArabic) {
    final colors = _getColors();
    final textStyle = _getTextStyle(context, isArabic);

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (!isArabic) ...[
            Icon(
              widget.icon,
              size: _getIconSize(),
              color: colors.foregroundColor,
            ),
            AppSpacing.horizontalSpaceSm,
          ],
          Text(
            widget.text,
            style: textStyle,
          ),
          if (isArabic) ...[
            AppSpacing.horizontalSpaceSm,
            Icon(
              widget.icon,
              size: _getIconSize(),
              color: colors.foregroundColor,
            ),
          ],
        ],
      );
    }

    return Text(
      widget.text,
      style: textStyle,
      textAlign: TextAlign.center,
    );
  }

  Widget _buildLoadingContent(BuildContext context, _ButtonColors colors) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: _getIconSize(),
          height: _getIconSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(colors.foregroundColor),
          ),
        ),
        AppSpacing.horizontalSpaceSm,
        Text(
          widget.text,
          style: _getTextStyle(context, false),
        ),
      ],
    );
  }

  _ButtonColors _getColors() {
    switch (widget.variant) {
      case ButtonVariant.primary:
        return const _ButtonColors(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.onPrimary,
        );
      case ButtonVariant.secondary:
        return const _ButtonColors(
          backgroundColor: AppColors.secondary,
          foregroundColor: AppColors.onSecondary,
        );
      case ButtonVariant.tertiary:
        return const _ButtonColors(
          backgroundColor: AppColors.tertiary,
          foregroundColor: AppColors.onTertiary,
        );
      case ButtonVariant.success:
        return const _ButtonColors(
          backgroundColor: AppColors.success,
          foregroundColor: AppColors.onSuccess,
        );
      case ButtonVariant.warning:
        return const _ButtonColors(
          backgroundColor: AppColors.warning,
          foregroundColor: AppColors.onWarning,
        );
      case ButtonVariant.error:
        return const _ButtonColors(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.onError,
        );
      case ButtonVariant.ghost:
        return const _ButtonColors(
          backgroundColor: Colors.transparent,
          foregroundColor: AppColors.primary,
          borderColor: AppColors.outline,
        );
    }
  }

  EdgeInsets _getPadding() {
    switch (widget.size) {
      case ButtonSize.small:
        return AppSpacing.buttonPaddingSmall;
      case ButtonSize.medium:
        return AppSpacing.buttonPaddingMedium;
      case ButtonSize.large:
        return AppSpacing.buttonPaddingLarge;
    }
  }

  TextStyle _getTextStyle(BuildContext context, bool isArabic) {
    final colors = _getColors();

    switch (widget.size) {
      case ButtonSize.small:
        return AppTypography.labelMedium(context, isArabic: isArabic).copyWith(
          color: colors.foregroundColor,
          fontWeight: FontWeight.w600,
        );
      case ButtonSize.medium:
        return AppTypography.labelLarge(context, isArabic: isArabic).copyWith(
          color: colors.foregroundColor,
          fontWeight: FontWeight.w600,
        );
      case ButtonSize.large:
        return AppTypography.titleMedium(context, isArabic: isArabic).copyWith(
          color: colors.foregroundColor,
          fontWeight: FontWeight.w600,
        );
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case ButtonSize.small:
        return AppSpacing.iconSm;
      case ButtonSize.medium:
        return AppSpacing.iconMd;
      case ButtonSize.large:
        return AppSpacing.iconLg;
    }
  }
}

class _ButtonColors {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color? borderColor;

  const _ButtonColors({
    required this.backgroundColor,
    required this.foregroundColor,
    this.borderColor,
  });
}
