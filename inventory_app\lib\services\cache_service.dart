import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheService {
  static const Duration _defaultCacheDuration = Duration(minutes: 5);
  static const Duration _dashboardCacheDuration = Duration(minutes: 2);
  static const Duration _itemsCacheDuration = Duration(minutes: 10);
  
  final Map<String, CacheEntry> _memoryCache = {};
  SharedPreferences? _prefs;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Memory cache operations
  void setMemoryCache<T>(String key, T data, {Duration? duration}) {
    final expiry = DateTime.now().add(duration ?? _defaultCacheDuration);
    _memoryCache[key] = CacheEntry(data, expiry);
  }

  T? getMemoryCache<T>(String key) {
    final entry = _memoryCache[key];
    if (entry == null || entry.isExpired) {
      _memoryCache.remove(key);
      return null;
    }
    return entry.data as T?;
  }

  void clearMemoryCache([String? pattern]) {
    if (pattern == null) {
      _memoryCache.clear();
    } else {
      _memoryCache.removeWhere((key, value) => key.contains(pattern));
    }
  }

  // Persistent cache operations
  Future<void> setPersistentCache<T>(String key, T data, {Duration? duration}) async {
    if (_prefs == null) await initialize();
    
    final expiry = DateTime.now().add(duration ?? _defaultCacheDuration);
    final cacheData = {
      'data': jsonEncode(data),
      'expiry': expiry.millisecondsSinceEpoch,
    };
    
    await _prefs!.setString(key, jsonEncode(cacheData));
  }

  Future<T?> getPersistentCache<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    if (_prefs == null) await initialize();
    
    final cacheString = _prefs!.getString(key);
    if (cacheString == null) return null;
    
    try {
      final cacheData = jsonDecode(cacheString);
      final expiry = DateTime.fromMillisecondsSinceEpoch(cacheData['expiry']);
      
      if (DateTime.now().isAfter(expiry)) {
        await _prefs!.remove(key);
        return null;
      }
      
      final data = jsonDecode(cacheData['data']);
      return fromJson(data);
    } catch (e) {
      await _prefs!.remove(key);
      return null;
    }
  }

  Future<void> clearPersistentCache([String? pattern]) async {
    if (_prefs == null) await initialize();
    
    if (pattern == null) {
      await _prefs!.clear();
    } else {
      final keys = _prefs!.getKeys().where((key) => key.contains(pattern));
      for (final key in keys) {
        await _prefs!.remove(key);
      }
    }
  }

  // Cache invalidation
  void invalidateCache(String pattern) {
    clearMemoryCache(pattern);
    clearPersistentCache(pattern);
  }

  // Specific cache durations for different data types
  Duration getCacheDuration(CacheType type) {
    switch (type) {
      case CacheType.dashboard:
        return _dashboardCacheDuration;
      case CacheType.items:
        return _itemsCacheDuration;
      case CacheType.transactions:
        return _defaultCacheDuration;
      case CacheType.users:
        return _defaultCacheDuration;
    }
  }
}

class CacheEntry {
  final dynamic data;
  final DateTime expiry;

  CacheEntry(this.data, this.expiry);

  bool get isExpired => DateTime.now().isAfter(expiry);
}

enum CacheType {
  dashboard,
  items,
  transactions,
  users,
}

// Cache keys constants
class CacheKeys {
  static const String dashboardStats = 'dashboard_stats';
  static const String allItems = 'all_items';
  static const String lowStockItems = 'low_stock_items';
  static const String topUsedItems = 'top_used_items';
  static const String stockDistribution = 'stock_distribution';
  static const String recentTransactions = 'recent_transactions';
  static const String allUsers = 'all_users';
  
  // Parameterized cache keys
  static String itemsByCategory(String category) => 'items_category_$category';
  static String transactionsByItem(String itemId) => 'transactions_item_$itemId';
  static String transactionsByDateRange(String start, String end) => 'transactions_${start}_$end';
}
