import 'package:flutter/material.dart';

class AppSpacing {
  // Base spacing unit (8dp)
  static const double _baseUnit = 8.0;
  
  // Spacing scale based on Material Design 3
  static const double xs = _baseUnit * 0.5; // 4dp
  static const double sm = _baseUnit; // 8dp
  static const double md = _baseUnit * 2; // 16dp
  static const double lg = _baseUnit * 3; // 24dp
  static const double xl = _baseUnit * 4; // 32dp
  static const double xxl = _baseUnit * 6; // 48dp
  static const double xxxl = _baseUnit * 8; // 64dp
  
  // Semantic spacing
  static const double tiny = xs;
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;
  static const double extraLarge = xl;
  static const double huge = xxl;
  static const double massive = xxxl;
  
  // Component-specific spacing
  static const double cardPadding = md;
  static const double cardMargin = sm;
  static const double buttonPadding = md;
  static const double inputPadding = md;
  static const double listItemPadding = md;
  static const double sectionSpacing = lg;
  static const double pageMargin = md;
  
  // Layout spacing
  static const double screenPadding = md;
  static const double contentSpacing = lg;
  static const double elementSpacing = sm;
  static const double groupSpacing = md;
  
  // Icon sizes
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 48.0;
  
  // Border radius
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusMd = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 20.0;
  static const double radiusXxl = 24.0;
  
  // Elevation levels
  static const double elevation0 = 0.0;
  static const double elevation1 = 1.0;
  static const double elevation2 = 2.0;
  static const double elevation3 = 3.0;
  static const double elevation4 = 4.0;
  static const double elevation6 = 6.0;
  static const double elevation8 = 8.0;
  static const double elevation12 = 12.0;
  static const double elevation16 = 16.0;
  static const double elevation24 = 24.0;
  
  // Common EdgeInsets
  static const EdgeInsets paddingXs = EdgeInsets.all(xs);
  static const EdgeInsets paddingSm = EdgeInsets.all(sm);
  static const EdgeInsets paddingMd = EdgeInsets.all(md);
  static const EdgeInsets paddingLg = EdgeInsets.all(lg);
  static const EdgeInsets paddingXl = EdgeInsets.all(xl);
  
  static const EdgeInsets paddingHorizontalXs = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets paddingHorizontalSm = EdgeInsets.symmetric(horizontal: sm);
  static const EdgeInsets paddingHorizontalMd = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets paddingHorizontalLg = EdgeInsets.symmetric(horizontal: lg);
  static const EdgeInsets paddingHorizontalXl = EdgeInsets.symmetric(horizontal: xl);
  
  static const EdgeInsets paddingVerticalXs = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets paddingVerticalSm = EdgeInsets.symmetric(vertical: sm);
  static const EdgeInsets paddingVerticalMd = EdgeInsets.symmetric(vertical: md);
  static const EdgeInsets paddingVerticalLg = EdgeInsets.symmetric(vertical: lg);
  static const EdgeInsets paddingVerticalXl = EdgeInsets.symmetric(vertical: xl);
  
  // Screen-specific padding
  static const EdgeInsets screenPaddingAll = EdgeInsets.all(screenPadding);
  static const EdgeInsets screenPaddingHorizontal = EdgeInsets.symmetric(horizontal: screenPadding);
  static const EdgeInsets screenPaddingVertical = EdgeInsets.symmetric(vertical: screenPadding);
  
  // Card padding
  static const EdgeInsets cardPaddingAll = EdgeInsets.all(cardPadding);
  static const EdgeInsets cardPaddingHorizontal = EdgeInsets.symmetric(horizontal: cardPadding);
  static const EdgeInsets cardPaddingVertical = EdgeInsets.symmetric(vertical: cardPadding);
  
  // List item padding
  static const EdgeInsets listItemPaddingAll = EdgeInsets.all(listItemPadding);
  static const EdgeInsets listItemPaddingHorizontal = EdgeInsets.symmetric(horizontal: listItemPadding);
  static const EdgeInsets listItemPaddingVertical = EdgeInsets.symmetric(vertical: listItemPadding);
  
  // Button padding
  static const EdgeInsets buttonPaddingSmall = EdgeInsets.symmetric(horizontal: sm, vertical: xs);
  static const EdgeInsets buttonPaddingMedium = EdgeInsets.symmetric(horizontal: md, vertical: sm);
  static const EdgeInsets buttonPaddingLarge = EdgeInsets.symmetric(horizontal: lg, vertical: md);
  
  // Common SizedBox widgets for spacing
  static const Widget verticalSpaceXs = SizedBox(height: xs);
  static const Widget verticalSpaceSm = SizedBox(height: sm);
  static const Widget verticalSpaceMd = SizedBox(height: md);
  static const Widget verticalSpaceLg = SizedBox(height: lg);
  static const Widget verticalSpaceXl = SizedBox(height: xl);
  static const Widget verticalSpaceXxl = SizedBox(height: xxl);
  
  static const Widget horizontalSpaceXs = SizedBox(width: xs);
  static const Widget horizontalSpaceSm = SizedBox(width: sm);
  static const Widget horizontalSpaceMd = SizedBox(width: md);
  static const Widget horizontalSpaceLg = SizedBox(width: lg);
  static const Widget horizontalSpaceXl = SizedBox(width: xl);
  static const Widget horizontalSpaceXxl = SizedBox(width: xxl);
  
  // Common BorderRadius
  static BorderRadius get borderRadiusXs => BorderRadius.circular(radiusXs);
  static BorderRadius get borderRadiusSm => BorderRadius.circular(radiusSm);
  static BorderRadius get borderRadiusMd => BorderRadius.circular(radiusMd);
  static BorderRadius get borderRadiusLg => BorderRadius.circular(radiusLg);
  static BorderRadius get borderRadiusXl => BorderRadius.circular(radiusXl);
  static BorderRadius get borderRadiusXxl => BorderRadius.circular(radiusXxl);
  
  // Responsive spacing helpers
  static double getResponsiveSpacing(BuildContext context, {
    double mobile = md,
    double tablet = lg,
    double desktop = xl,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return mobile;
    } else if (screenWidth < 1200) {
      return tablet;
    } else {
      return desktop;
    }
  }
  
  static EdgeInsets getResponsivePadding(BuildContext context, {
    EdgeInsets mobile = paddingMd,
    EdgeInsets tablet = paddingLg,
    EdgeInsets desktop = paddingXl,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return mobile;
    } else if (screenWidth < 1200) {
      return tablet;
    } else {
      return desktop;
    }
  }
  
  // Grid spacing
  static const double gridSpacing = sm;
  static const double gridRunSpacing = sm;
  
  // Animation durations
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1200;
  static const double desktopBreakpoint = 1600;
  
  // Helper methods for responsive design
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }
  
  // Get appropriate column count for grid layouts
  static int getGridColumns(BuildContext context, {
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
  }) {
    if (isMobile(context)) {
      return mobile;
    } else if (isTablet(context)) {
      return tablet;
    } else {
      return desktop;
    }
  }
  
  // Get appropriate aspect ratio for cards
  static double getCardAspectRatio(BuildContext context, {
    double mobile = 1.2,
    double tablet = 1.5,
    double desktop = 1.8,
  }) {
    if (isMobile(context)) {
      return mobile;
    } else if (isTablet(context)) {
      return tablet;
    } else {
      return desktop;
    }
  }
}
