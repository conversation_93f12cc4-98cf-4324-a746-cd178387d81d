import '../models/item.dart';
import '../services/database_service.dart';
import '../services/cache_service.dart';
import '../services/logging_service.dart';

class PaginatedResult<T> {
  final List<T> items;
  final int totalCount;
  final int currentPage;
  final int pageSize;
  final bool hasNextPage;
  final bool hasPreviousPage;

  PaginatedResult({
    required this.items,
    required this.totalCount,
    required this.currentPage,
    required this.pageSize,
  })  : hasNextPage = (currentPage * pageSize) < totalCount,
        hasPreviousPage = currentPage > 1;

  int get totalPages => (totalCount / pageSize).ceil();
}

class ItemRepository {
  final DatabaseService _databaseService;
  final CacheService _cacheService;
  final LoggingService _loggingService;

  ItemRepository(
      this._databaseService, this._cacheService, this._loggingService);

  Future<PaginatedResult<Item>> getItems({
    int page = 1,
    int pageSize = 20,
    String? category,
    String? searchQuery,
    bool? isActive,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = _buildCacheKey(
          'items', page, pageSize, category, searchQuery, isActive);
      final cachedResult =
          _cacheService.getMemoryCache<PaginatedResult<Item>>(cacheKey);

      if (cachedResult != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedResult;
      }

      _loggingService.logCacheMiss(cacheKey);

      final result = await _getItemsPaginated(
          page, pageSize, category, searchQuery, isActive);

      _cacheService.setMemoryCache(
        cacheKey,
        result,
        duration: _cacheService.getCacheDuration(CacheType.items),
      );

      _loggingService.logPerformance('getItems', stopwatch.elapsed);
      return result;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get items',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<PaginatedResult<Item>> _getItemsPaginated(
    int page,
    int pageSize,
    String? category,
    String? searchQuery,
    bool? isActive,
  ) async {
    final supabase = _databaseService.supabase;
    final offset = (page - 1) * pageSize;

    // Build query
    var query = supabase.from('items').select('*');

    // Apply filters
    if (isActive != null) {
      query = query.eq('is_active', isActive);
    }

    if (category != null && category.isNotEmpty) {
      query = query.eq('category', category);
    }

    if (searchQuery != null && searchQuery.isNotEmpty) {
      query = query.or('name.ilike.%$searchQuery%,code.ilike.%$searchQuery%');
    }

    // Apply pagination and ordering
    final response = await query
        .order('updated_at', ascending: false)
        .range(offset, offset + pageSize - 1);

    final items =
        (response as List<dynamic>).map((json) => Item.fromJson(json)).toList();

    // For total count, we need a separate query
    final countResponse = await supabase.from('items').select('id').count();
    final totalCount = countResponse.count;

    return PaginatedResult<Item>(
      items: items,
      totalCount: totalCount,
      currentPage: page,
      pageSize: pageSize,
    );
  }

  Future<List<Item>> getLowStockItems() async {
    final stopwatch = Stopwatch()..start();

    try {
      final cachedItems =
          _cacheService.getMemoryCache<List<Item>>(CacheKeys.lowStockItems);

      if (cachedItems != null) {
        _loggingService.logCacheHit(CacheKeys.lowStockItems);
        return cachedItems;
      }

      _loggingService.logCacheMiss(CacheKeys.lowStockItems);

      // Optimized query using database filtering
      final supabase = _databaseService.supabase;
      final data = await supabase
          .from('items')
          .select()
          .eq('is_active', true)
          .filter('quantity', 'lte', 'min_quantity');

      final items =
          (data as List<dynamic>).map((json) => Item.fromJson(json)).toList();

      _cacheService.setMemoryCache(
        CacheKeys.lowStockItems,
        items,
        duration: _cacheService.getCacheDuration(CacheType.items),
      );

      _loggingService.logPerformance('getLowStockItems', stopwatch.elapsed);
      return items;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get low stock items',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<List<String>> getCategories() async {
    final stopwatch = Stopwatch()..start();

    try {
      const cacheKey = 'item_categories';
      final cachedCategories =
          _cacheService.getMemoryCache<List<String>>(cacheKey);

      if (cachedCategories != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedCategories;
      }

      _loggingService.logCacheMiss(cacheKey);

      final supabase = _databaseService.supabase;
      final data =
          await supabase.from('items').select('category').eq('is_active', true);

      final categories = (data as List<dynamic>)
          .map((item) => item['category'] as String? ?? 'other')
          .toSet()
          .toList()
        ..sort();

      _cacheService.setMemoryCache(
        cacheKey,
        categories,
        duration: const Duration(hours: 1), // Categories change less frequently
      );

      _loggingService.logPerformance('getCategories', stopwatch.elapsed);
      return categories;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get categories',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<Item?> getItemById(String id) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = 'item_$id';
      final cachedItem = _cacheService.getMemoryCache<Item>(cacheKey);

      if (cachedItem != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedItem;
      }

      _loggingService.logCacheMiss(cacheKey);

      final item = await _databaseService.getItemById(id);

      if (item != null) {
        _cacheService.setMemoryCache(
          cacheKey,
          item,
          duration: _cacheService.getCacheDuration(CacheType.items),
        );
      }

      _loggingService.logPerformance('getItemById', stopwatch.elapsed);
      return item;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get item by id',
          error: e, stackTrace: stackTrace);
      return null;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> insertItem(Item item) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.insertItem(item);
      _invalidateItemCaches();
      _loggingService.logInfo('Item inserted: ${item.name}');
      _loggingService.logPerformance('insertItem', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to insert item',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> updateItem(Item item) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.updateItem(item);
      _invalidateItemCaches();
      _cacheService
          .clearMemoryCache('item_${item.id}'); // Clear specific item cache
      _loggingService.logInfo('Item updated: ${item.name}');
      _loggingService.logPerformance('updateItem', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to update item',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> deleteItem(String id) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.deleteItem(id);
      _invalidateItemCaches();
      _cacheService.clearMemoryCache('item_$id'); // Clear specific item cache
      _loggingService.logInfo('Item deleted: $id');
      _loggingService.logPerformance('deleteItem', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to delete item',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  void _invalidateItemCaches() {
    _cacheService.invalidateCache('items');
    _cacheService.invalidateCache('low_stock');
    _cacheService.invalidateCache('categories');
    _cacheService
        .invalidateCache('dashboard'); // Dashboard depends on item data
  }

  String _buildCacheKey(String prefix, int page, int pageSize, String? category,
      String? searchQuery, bool? isActive) {
    final parts = [prefix, page.toString(), pageSize.toString()];
    if (category != null) parts.add('cat_$category');
    if (searchQuery != null) parts.add('search_$searchQuery');
    if (isActive != null) parts.add('active_$isActive');
    return parts.join('_');
  }
}
