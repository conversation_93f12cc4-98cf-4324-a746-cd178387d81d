import '../models/transaction.dart';
import '../services/database_service.dart';
import '../services/cache_service.dart';
import '../services/logging_service.dart';
import 'item_repository.dart';

class TransactionRepository {
  final DatabaseService _databaseService;
  final CacheService _cacheService;
  final LoggingService _loggingService;

  TransactionRepository(
      this._databaseService, this._cacheService, this._loggingService);

  Future<PaginatedResult<Transaction>> getTransactions({
    int page = 1,
    int pageSize = 20,
    String? itemId,
    TransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = _buildCacheKey(
          'transactions', page, pageSize, itemId, type, startDate, endDate);
      final cachedResult =
          _cacheService.getMemoryCache<PaginatedResult<Transaction>>(cacheKey);

      if (cachedResult != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedResult;
      }

      _loggingService.logCacheMiss(cacheKey);

      final result = await _getTransactionsPaginated(
          page, pageSize, itemId, type, startDate, endDate);

      _cacheService.setMemoryCache(
        cacheKey,
        result,
        duration: _cacheService.getCacheDuration(CacheType.transactions),
      );

      _loggingService.logPerformance('getTransactions', stopwatch.elapsed);
      return result;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get transactions',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<PaginatedResult<Transaction>> _getTransactionsPaginated(
    int page,
    int pageSize,
    String? itemId,
    TransactionType? type,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final supabase = _databaseService.supabase;
    final offset = (page - 1) * pageSize;

    // Build query with joins for better performance
    var query = supabase.from('transactions').select('''
          *,
          items!inner(name, code, category)
        ''');

    // Apply filters
    if (itemId != null) {
      query = query.eq('item_id', itemId);
    }

    if (type != null) {
      query = query.eq('type', type.toString().split('.').last);
    }

    if (startDate != null) {
      query = query.gte('date', startDate.toIso8601String());
    }

    if (endDate != null) {
      query = query.lte('date', endDate.toIso8601String());
    }

    // Apply pagination and ordering
    final response = await query
        .order('date', ascending: false)
        .range(offset, offset + pageSize - 1);

    final transactions = (response as List<dynamic>)
        .map((json) => Transaction.fromJson(json))
        .toList();

    // Get total count with a separate query
    final countResponse =
        await supabase.from('transactions').select('id').count();
    final totalCount = countResponse.count;

    return PaginatedResult<Transaction>(
      items: transactions,
      totalCount: totalCount,
      currentPage: page,
      pageSize: pageSize,
    );
  }

  Future<List<Transaction>> getRecentTransactions({int limit = 10}) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = '${CacheKeys.recentTransactions}_$limit';
      final cachedTransactions =
          _cacheService.getMemoryCache<List<Transaction>>(cacheKey);

      if (cachedTransactions != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedTransactions;
      }

      _loggingService.logCacheMiss(cacheKey);

      final supabase = _databaseService.supabase;
      final data = await supabase.from('transactions').select('''
            *,
            items!inner(name, code, category)
          ''').order('date', ascending: false).limit(limit);

      final transactions = (data as List<dynamic>)
          .map((json) => Transaction.fromJson(json))
          .toList();

      _cacheService.setMemoryCache(
        cacheKey,
        transactions,
        duration: _cacheService.getCacheDuration(CacheType.transactions),
      );

      _loggingService.logPerformance(
          'getRecentTransactions', stopwatch.elapsed);
      return transactions;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get recent transactions',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<List<Transaction>> getTransactionsByItem(String itemId) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = CacheKeys.transactionsByItem(itemId);
      final cachedTransactions =
          _cacheService.getMemoryCache<List<Transaction>>(cacheKey);

      if (cachedTransactions != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedTransactions;
      }

      _loggingService.logCacheMiss(cacheKey);

      final transactions = await _databaseService.getTransactionsByItem(itemId);

      _cacheService.setMemoryCache(
        cacheKey,
        transactions,
        duration: _cacheService.getCacheDuration(CacheType.transactions),
      );

      _loggingService.logPerformance(
          'getTransactionsByItem', stopwatch.elapsed);
      return transactions;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get transactions by item',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<Map<String, dynamic>> getTransactionStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = _buildStatsCacheKey(startDate, endDate);
      final cachedStats =
          _cacheService.getMemoryCache<Map<String, dynamic>>(cacheKey);

      if (cachedStats != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedStats;
      }

      _loggingService.logCacheMiss(cacheKey);

      final stats = await _getTransactionStats(startDate, endDate);

      _cacheService.setMemoryCache(
        cacheKey,
        stats,
        duration: _cacheService.getCacheDuration(CacheType.transactions),
      );

      _loggingService.logPerformance('getTransactionStats', stopwatch.elapsed);
      return stats;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get transaction stats',
          error: e, stackTrace: stackTrace);
      return {};
    } finally {
      stopwatch.stop();
    }
  }

  Future<Map<String, dynamic>> _getTransactionStats(
      DateTime? startDate, DateTime? endDate) async {
    final supabase = _databaseService.supabase;

    var query = supabase.from('transactions').select('type, quantity');

    if (startDate != null) {
      query = query.gte('date', startDate.toIso8601String());
    }

    if (endDate != null) {
      query = query.lte('date', endDate.toIso8601String());
    }

    final data = await query;

    double totalIncoming = 0.0;
    double totalOutgoing = 0.0;
    int incomingCount = 0;
    int outgoingCount = 0;

    for (final transaction in data) {
      final type = transaction['type'] as String;
      final quantity = (transaction['quantity'] as num).toDouble();

      if (type == 'incoming') {
        totalIncoming += quantity;
        incomingCount++;
      } else {
        totalOutgoing += quantity;
        outgoingCount++;
      }
    }

    return {
      'totalIncoming': totalIncoming,
      'totalOutgoing': totalOutgoing,
      'incomingCount': incomingCount,
      'outgoingCount': outgoingCount,
      'netChange': totalIncoming - totalOutgoing,
    };
  }

  Future<void> insertTransaction(Transaction transaction) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.insertTransaction(transaction);
      _invalidateTransactionCaches();
      _loggingService.logInfo(
          'Transaction inserted: ${transaction.type} - ${transaction.quantity}');
      _loggingService.logPerformance('insertTransaction', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to insert transaction',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> updateTransaction(Transaction transaction) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.updateTransaction(transaction);
      _invalidateTransactionCaches();
      _loggingService.logInfo('Transaction updated: ${transaction.id}');
      _loggingService.logPerformance('updateTransaction', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to update transaction',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> deleteTransaction(String id) async {
    final stopwatch = Stopwatch()..start();

    try {
      await _databaseService.deleteTransaction(id);
      _invalidateTransactionCaches();
      _loggingService.logInfo('Transaction deleted: $id');
      _loggingService.logPerformance('deleteTransaction', stopwatch.elapsed);
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to delete transaction',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  void _invalidateTransactionCaches() {
    _cacheService.invalidateCache('transactions');
    _cacheService.invalidateCache('recent_transactions');
    _cacheService
        .invalidateCache('dashboard'); // Dashboard depends on transaction data
  }

  String _buildCacheKey(String prefix, int page, int pageSize, String? itemId,
      TransactionType? type, DateTime? startDate, DateTime? endDate) {
    final parts = [prefix, page.toString(), pageSize.toString()];
    if (itemId != null) parts.add('item_$itemId');
    if (type != null) parts.add('type_${type.toString().split('.').last}');
    if (startDate != null) {
      parts.add('start_${startDate.millisecondsSinceEpoch}');
    }
    if (endDate != null) {
      parts.add('end_${endDate.millisecondsSinceEpoch}');
    }
    return parts.join('_');
  }

  String _buildStatsCacheKey(DateTime? startDate, DateTime? endDate) {
    final parts = ['transaction_stats'];
    if (startDate != null) {
      parts.add('start_${startDate.millisecondsSinceEpoch}');
    }
    if (endDate != null) {
      parts.add('end_${endDate.millisecondsSinceEpoch}');
    }
    return parts.join('_');
  }
}
