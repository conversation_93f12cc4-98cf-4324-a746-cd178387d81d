import 'dart:async';
import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_texts.dart';
import '../../core/service_locator.dart';
import '../../repositories/item_repository.dart';
import '../../models/item.dart';
import '../../services/logging_service.dart';
import '../../widgets/app_drawer.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/pagination_widget.dart';
import '../../utils/memory_manager.dart';

class OptimizedItemListView extends StatefulWidget {
  const OptimizedItemListView({Key? key}) : super(key: key);

  @override
  State<OptimizedItemListView> createState() => _OptimizedItemListViewState();
}

class _OptimizedItemListViewState extends State<OptimizedItemListView>
    with AutoMemoryManagement, AutomaticKeepAliveClientMixin {
  late final ItemRepository _itemRepository;

  PaginatedResult<Item>? _paginatedItems;
  List<String> _categories = [];

  bool _isLoading = true;
  String? _errorMessage;

  // Filter and pagination state
  int _currentPage = 1;
  int _pageSize = 20;
  String? _selectedCategory;
  String _searchQuery = '';
  bool? _isActiveFilter = true;

  // Controllers
  late final TextEditingController _searchController;
  late final ScrollController _scrollController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _itemRepository = serviceLocator<ItemRepository>();
    _searchController = TextEditingController();
    _scrollController = ScrollController();

    // Add controllers to memory management
    addNotifier(_searchController);

    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadCategories(),
      _loadItems(),
    ]);
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _itemRepository.getCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
        });
      }
    } catch (e) {
      // Categories are not critical, so we don't show error for this
      serviceLocator<LoggingService>()
          .logError('Failed to load categories', error: e);
    }
  }

  Future<void> _loadItems({bool showLoading = true}) async {
    if (!mounted) return;

    if (showLoading) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      final result = await _itemRepository.getItems(
        page: _currentPage,
        pageSize: _pageSize,
        category: _selectedCategory,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
        isActive: _isActiveFilter,
      );

      if (mounted) {
        setState(() {
          _paginatedItems = result;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadItems();

    // Scroll to top when page changes
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _onPageSizeChanged(int pageSize) {
    setState(() {
      _pageSize = pageSize;
      _currentPage = 1; // Reset to first page
    });
    _loadItems();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _currentPage = 1; // Reset to first page
    });

    // Debounce search
    final searchTimer = Timer(const Duration(milliseconds: 500), () {
      _loadItems();
    });
    addTimer(searchTimer);
  }

  void _onCategoryChanged(String? category) {
    setState(() {
      _selectedCategory = category;
      _currentPage = 1; // Reset to first page
    });
    _loadItems();
  }

  void _onActiveFilterChanged(bool? isActive) {
    setState(() {
      _isActiveFilter = isActive;
      _currentPage = 1; // Reset to first page
    });
    _loadItems();
  }

  Future<void> _refreshItems() async {
    // Invalidate cache and reload
    _itemRepository.invalidateItemCaches();
    await _loadItems(showLoading: false);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? AppTexts.inventory : AppTexts.inventoryEn),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textDark,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshItems,
            tooltip: isArabic ? 'تحديث' : 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddItemDialog(context),
            tooltip: isArabic ? 'إضافة صنف' : 'Add Item',
          ),
        ],
      ),
      drawer: const AppDrawer(currentRoute: '/items'),
      body: Column(
        children: [
          _buildFilters(context, isArabic),
          Expanded(
            child: _buildBody(context, isArabic),
          ),
          if (_paginatedItems != null)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      PageSizeSelector(
                        currentPageSize: _pageSize,
                        onPageSizeChanged: _onPageSizeChanged,
                      ),
                      const SizedBox(width: 16),
                    ],
                  ),
                  const SizedBox(height: 8),
                  PaginationWidget(
                    paginatedResult: _paginatedItems!,
                    onPageChanged: _onPageChanged,
                    isLoading: _isLoading,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFilters(BuildContext context, bool isArabic) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: isArabic ? 'البحث في الأصناف...' : 'Search items...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _onSearchChanged('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: AppColors.primary),
              ),
            ),
          ),
          const SizedBox(height: 12),

          // Filter row
          Row(
            children: [
              // Category filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: isArabic ? 'الفئة' : 'Category',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    DropdownMenuItem<String>(
                      value: null,
                      child: Text(isArabic ? 'جميع الفئات' : 'All Categories'),
                    ),
                    ..._categories.map((category) => DropdownMenuItem<String>(
                          value: category,
                          child: Text(category),
                        )),
                  ],
                  onChanged: _onCategoryChanged,
                ),
              ),
              const SizedBox(width: 12),

              // Active filter
              Expanded(
                child: DropdownButtonFormField<bool?>(
                  value: _isActiveFilter,
                  decoration: InputDecoration(
                    labelText: isArabic ? 'الحالة' : 'Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    DropdownMenuItem<bool?>(
                      value: null,
                      child: Text(isArabic ? 'الكل' : 'All'),
                    ),
                    DropdownMenuItem<bool?>(
                      value: true,
                      child: Text(isArabic ? 'نشط' : 'Active'),
                    ),
                    DropdownMenuItem<bool?>(
                      value: false,
                      child: Text(isArabic ? 'غير نشط' : 'Inactive'),
                    ),
                  ],
                  onChanged: _onActiveFilterChanged,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context, bool isArabic) {
    if (_isLoading) {
      return LoadingWidget(
        message: isArabic ? 'جاري تحميل الأصناف...' : 'Loading items...',
      );
    }

    if (_errorMessage != null) {
      return ErrorDisplayWidget(
        message: _errorMessage!,
        onRetry: () => _loadItems(),
      );
    }

    if (_paginatedItems == null || _paginatedItems!.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: AppColors.textLight,
            ),
            const SizedBox(height: 16),
            Text(
              isArabic ? 'لا توجد أصناف' : 'No items found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.textLight,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              isArabic
                  ? 'قم بإضافة أصناف جديدة'
                  : 'Add new items to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textLight,
                  ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshItems,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _paginatedItems!.items.length,
        itemBuilder: (context, index) {
          final item = _paginatedItems!.items[index];
          return _buildItemCard(context, item, isArabic);
        },
      ),
    );
  }

  Widget _buildItemCard(BuildContext context, Item item, bool isArabic) {
    final isLowStock = item.quantity <= item.minQuantity;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isLowStock
              ? AppColors.warning.withValues(alpha: 0.3)
              : Colors.transparent,
          width: isLowStock ? 2 : 0,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isLowStock
                ? AppColors.warning.withValues(alpha: 0.1)
                : AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.inventory_2,
            color: isLowStock ? AppColors.warning : AppColors.primary,
          ),
        ),
        title: Text(
          item.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${isArabic ? 'الكود:' : 'Code:'} ${item.code}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              '${isArabic ? 'الفئة:' : 'Category:'} ${item.category}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            if (isLowStock)
              Text(
                isArabic ? 'مخزون منخفض!' : 'Low Stock!',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.bold,
                    ),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              item.quantity.toString(),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isLowStock ? AppColors.warning : AppColors.success,
                  ),
            ),
            Text(
              item.unit.toString().split('.').last,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textLight,
                  ),
            ),
          ],
        ),
        onTap: () => _showItemDetails(context, item),
      ),
    );
  }

  void _showItemDetails(BuildContext context, Item item) {
    // Navigate to item details page
    // Implementation depends on your routing setup
  }

  void _showAddItemDialog(BuildContext context) {
    // Show add item dialog
    // Implementation depends on your form setup
  }
}
