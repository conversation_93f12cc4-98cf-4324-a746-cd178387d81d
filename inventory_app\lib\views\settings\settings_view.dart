import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_texts.dart';
import '../../services/database_service.dart';
import '../../services/backup_service.dart';
import '../../widgets/app_drawer.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../../models/transaction.dart';

class SettingsView extends StatefulWidget {
  final bool isDarkMode;
  final ValueChanged<bool> onDarkModeChanged;
  const SettingsView(
      {Key? key, this.isDarkMode = false, required this.onDarkModeChanged})
      : super(key: key);

  @override
  State<SettingsView> createState() => _SettingsViewState();
}

class _SettingsViewState extends State<SettingsView> {
  final DatabaseService _databaseService = DatabaseService();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // إعدادات النظام
  bool _notificationsEnabled = true;
  bool _autoBackupEnabled = true;
  String _language = 'العربية';
  String _currency = 'جنيه مصري';
  int _backupFrequency = 7; // أيام
  double _lowStockThreshold = 10.0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    // هنا يمكن تحميل الإعدادات من التخزين المحلي
    // حالياً نستخدم القيم الافتراضية
  }

  String _getBackupFrequencyDisplay() {
    switch (_backupFrequency) {
      case 1:
        return 'كل يوم';
      case 3:
        return 'كل 3 أيام';
      case 7:
        return 'كل أسبوع';
      case 30:
        return 'كل شهر';
      default:
        return 'كل أسبوع';
    }
  }

  Future<void> _saveSettings() async {
    // هنا يمكن حفظ الإعدادات إلى التخزين المحلي
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حفظ الإعدادات بنجاح')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.menu, color: AppColors.textDark),
          onPressed: () => _scaffoldKey.currentState?.openDrawer(),
        ),
        title: const Text('الإعدادات'),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textDark,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      drawer: const AppDrawer(currentRoute: '/settings'),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: ListView(
          padding: const EdgeInsets.all(20),
          children: [
            // إعدادات عامة
            _buildSectionCard(
              title: 'إعدادات عامة',
              icon: Icons.settings,
              children: [
                _buildSwitchTile(
                  title: 'الإشعارات',
                  subtitle: 'تفعيل إشعارات النظام',
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                  icon: Icons.notifications,
                ),
                _buildSwitchTile(
                  title: 'النسخ الاحتياطي التلقائي',
                  subtitle: 'إنشاء نسخة احتياطية تلقائياً',
                  value: _autoBackupEnabled,
                  onChanged: (value) {
                    setState(() {
                      _autoBackupEnabled = value;
                    });
                  },
                  icon: Icons.backup,
                ),
                _buildSwitchTile(
                  title: 'الوضع المظلم',
                  subtitle: 'تفعيل المظهر الداكن',
                  value: widget.isDarkMode,
                  onChanged: widget.onDarkModeChanged,
                  icon: Icons.dark_mode,
                ),
                _buildDropdownTile(
                  title: 'اللغة',
                  subtitle: 'اختر لغة التطبيق',
                  value: _language,
                  items: ['العربية', 'English'],
                  onChanged: (value) {
                    setState(() {
                      _language = value!;
                    });
                  },
                  icon: Icons.language,
                ),
                _buildDropdownTile(
                  title: 'العملة',
                  subtitle: 'اختر عملة النظام',
                  value: _currency,
                  items: [
                    'جنيه مصري',
                    'دولار أمريكي',
                    'ريال سعودي',
                    'درهم إماراتي'
                  ],
                  onChanged: (value) {
                    setState(() {
                      _currency = value!;
                    });
                  },
                  icon: Icons.attach_money,
                ),
              ],
            ),

            const SizedBox(height: 20),

            // إعدادات المخزون
            _buildSectionCard(
              title: 'إعدادات المخزون',
              icon: Icons.inventory,
              children: [
                _buildSliderTile(
                  title: 'حد المخزون المنخفض',
                  subtitle: 'النسبة المئوية للتنبيه',
                  value: _lowStockThreshold,
                  min: 1.0,
                  max: 50.0,
                  divisions: 49,
                  onChanged: (value) {
                    setState(() {
                      _lowStockThreshold = value;
                    });
                  },
                  icon: Icons.warning,
                ),
                _buildDropdownTile(
                  title: 'تكرار النسخ الاحتياطي',
                  subtitle: 'عدد الأيام بين النسخ',
                  value: _getBackupFrequencyDisplay(),
                  items: ['كل يوم', 'كل 3 أيام', 'كل أسبوع', 'كل شهر'],
                  onChanged: (value) {
                    setState(() {
                      switch (value) {
                        case 'كل يوم':
                          _backupFrequency = 1;
                          break;
                        case 'كل 3 أيام':
                          _backupFrequency = 3;
                          break;
                        case 'كل أسبوع':
                          _backupFrequency = 7;
                          break;
                        case 'كل شهر':
                          _backupFrequency = 30;
                          break;
                      }
                    });
                  },
                  icon: Icons.schedule,
                ),
                _buildActionTile(
                  title: 'إعادة حساب المخزون',
                  subtitle: 'إعادة حساب كميات الأصناف بناءً على الحركات',
                  icon: Icons.calculate,
                  onTap: () => _showRecalculateInventoryDialog(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // إدارة البيانات
            _buildSectionCard(
              title: 'إدارة البيانات',
              icon: Icons.storage,
              children: [
                _buildActionTile(
                  title: 'إنشاء نسخة احتياطية',
                  subtitle: 'تصدير جميع البيانات',
                  icon: Icons.backup,
                  onTap: () => _showBackupDialog(),
                ),
                _buildActionTile(
                  title: 'استعادة نسخة احتياطية',
                  subtitle: 'استيراد البيانات من ملف',
                  icon: Icons.restore,
                  onTap: () => _showRestoreDialog(),
                ),
                _buildActionTile(
                  title: 'تصدير البيانات',
                  subtitle: 'تصدير البيانات بصيغة Excel',
                  icon: Icons.file_download,
                  onTap: () => _showExportDialog(),
                ),
                _buildActionTile(
                  title: 'مسح جميع البيانات',
                  subtitle: 'حذف جميع البيانات نهائياً',
                  icon: Icons.delete_forever,
                  onTap: () => _showClearDataDialog(),
                  isDestructive: true,
                ),
              ],
            ),

            const SizedBox(height: 20),

            // معلومات النظام
            _buildSectionCard(
              title: 'معلومات النظام',
              icon: Icons.info,
              children: [
                _buildInfoTile(
                  title: 'إصدار التطبيق',
                  subtitle: '1.0.0',
                  icon: Icons.app_settings_alt,
                ),
                _buildInfoTile(
                  title: 'تاريخ آخر تحديث',
                  subtitle: '2024-01-15',
                  icon: Icons.update,
                ),
                _buildInfoTile(
                  title: 'حجم قاعدة البيانات',
                  subtitle: '2.5 MB',
                  icon: Icons.storage,
                ),
                _buildActionTile(
                  title: 'تحديث التطبيق',
                  subtitle: 'التحقق من وجود تحديثات جديدة',
                  icon: Icons.system_update,
                  onTap: () => _checkForUpdates(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // حول التطبيق
            _buildSectionCard(
              title: 'حول التطبيق',
              icon: Icons.help,
              children: [
                _buildActionTile(
                  title: 'دليل المستخدم',
                  subtitle: 'تعلم كيفية استخدام التطبيق',
                  icon: Icons.help_outline,
                  onTap: () => _showUserGuide(),
                ),
                _buildActionTile(
                  title: 'سياسة الخصوصية',
                  subtitle: 'قراءة سياسة الخصوصية',
                  icon: Icons.privacy_tip,
                  onTap: () => _showPrivacyPolicy(),
                ),
                _buildActionTile(
                  title: 'شروط الاستخدام',
                  subtitle: 'قراءة شروط الاستخدام',
                  icon: Icons.description,
                  onTap: () => _showTermsOfService(),
                ),
                _buildActionTile(
                  title: 'تواصل معنا',
                  subtitle: 'إرسال رسالة للدعم الفني',
                  icon: Icons.contact_support,
                  onTap: () => _contactSupport(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: AppColors.cardBorder,
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: AppColors.cardGradient,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                        ),
                  ),
                ],
              ),
            ),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textLight,
          fontSize: 12,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textLight,
          fontSize: 12,
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        items: items.map((item) {
          return DropdownMenuItem(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
        underline: Container(),
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: TextStyle(
              color: AppColors.textLight,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
          Text(
            '${value.toInt()}%',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isDestructive
              ? AppColors.error.withValues(alpha: 0.1)
              : AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? AppColors.error : AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: isDestructive ? AppColors.error : AppColors.textDark,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textLight,
          fontSize: 12,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.textLight,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppColors.primary,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: AppColors.textDark,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: AppColors.textLight,
          fontSize: 12,
        ),
      ),
    );
  }

  // دوال الإجراءات
  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إنشاء نسخة احتياطية'),
        content: const Text('هل تريد إنشاء نسخة احتياطية من جميع البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              // إظهار مؤشر التحميل
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              try {
                final backupPath = await BackupService.createBackup();

                // إخفاء مؤشر التحميل
                Navigator.pop(context);

                if (backupPath != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          'تم إنشاء النسخة الاحتياطية بنجاح\nالمسار: $backupPath'),
                      duration: const Duration(seconds: 5),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('فشل في إنشاء النسخة الاحتياطية'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                // إخفاء مؤشر التحميل
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في إنشاء النسخة الاحتياطية: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة نسخة احتياطية'),
        content: const Text(
            'هل تريد استعادة البيانات من النسخة الاحتياطية؟\nتحذير: سيتم استبدال جميع البيانات الحالية.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              // إظهار مؤشر التحميل
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              try {
                // الحصول على قائمة النسخ الاحتياطية المتاحة
                final backups = await BackupService.getAvailableBackups();

                if (backups.isEmpty) {
                  // إخفاء مؤشر التحميل
                  Navigator.pop(context);

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('لا توجد نسخ احتياطية متاحة'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  return;
                }

                // اختيار أحدث نسخة احتياطية
                final latestBackup = backups.last;
                final success =
                    await BackupService.restoreFromJson(latestBackup.path);

                // إخفاء مؤشر التحميل
                Navigator.pop(context);

                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم استعادة البيانات بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('فشل في استعادة البيانات'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                // إخفاء مؤشر التحميل
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في استعادة البيانات: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: const Text('هل تريد تصدير جميع البيانات بصيغة Excel؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              // إظهار مؤشر التحميل
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const Center(
                  child: CircularProgressIndicator(),
                ),
              );

              try {
                // جلب البيانات
                final items = await _databaseService.getAllItems();
                final transactions =
                    await _databaseService.getAllTransactions();
                final users = await _databaseService.getAllUsers();

                // إنشاء ملف Excel محسن
                final excel = Excel.createExcel();

                // صفحة الأصناف
                final itemsSheet = excel['الأصناف'];
                _addItemsToSheet(itemsSheet, items);

                // صفحة العمليات
                final transactionsSheet = excel['العمليات'];
                _addTransactionsToSheet(transactionsSheet, transactions, items);

                // صفحة المستخدمين
                final usersSheet = excel['المستخدمين'];
                _addUsersToSheet(usersSheet, users);

                // صفحة الملخص
                final summarySheet = excel['الملخص'];
                _addSummaryToSheet(summarySheet, items, transactions, users);

                // حفظ الملف
                final directory = await getApplicationDocumentsDirectory();
                final exportDir = Directory('${directory.path}/exports');
                if (!await exportDir.exists()) {
                  await exportDir.create(recursive: true);
                }

                final fileName =
                    'inventory_export_${DateTime.now().millisecondsSinceEpoch}.xlsx';
                final file = File('${exportDir.path}/$fileName');
                await file.writeAsBytes(excel.encode()!);

                // إخفاء مؤشر التحميل
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تصدير البيانات بنجاح إلى:\n${file.path}'),
                    duration: const Duration(seconds: 5),
                    action: SnackBarAction(
                      label: 'فتح الملف',
                      onPressed: () => BackupService.openBackupFile(file.path),
                    ),
                  ),
                );
              } catch (e) {
                // إخفاء مؤشر التحميل
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('حدث خطأ أثناء التصدير: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  // دوال مساعدة لإضافة البيانات إلى أوراق Excel
  void _addItemsToSheet(Sheet sheet, List<dynamic> items) {
    // العناوين
    final headers = [
      'الكود',
      'الاسم',
      'الفئة',
      'الكمية',
      'الوحدة',
      'الحد الأدنى',
      'الحالة',
      'تاريخ الإنشاء'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = headers[i];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        backgroundColorHex: '#E3F2FD',
      );
    }

    // البيانات
    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = item.code;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = item.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = item.categoryDisplayName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = item.quantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = item.unitDisplayName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = item.minQuantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = item.quantity <= item.minQuantity ? 'منخفض' : 'طبيعي';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = item.createdAt.toString().split(' ')[0];
    }
  }

  void _addTransactionsToSheet(
      Sheet sheet, List<dynamic> transactions, List<dynamic> items) {
    // العناوين
    final headers = [
      'التاريخ',
      'اسم الصنف',
      'نوع العملية',
      'الكمية',
      'الكمية الحالية',
      'رقم كشف الطلب',
      'السنترال',
      'اسم المنصرف إليه',
      'أسباب الصرف',
      'الشخص المسؤول',
      'الملاحظات'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = headers[i];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        backgroundColorHex: '#E3F2FD',
      );
    }

    // البيانات
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      final item = items.firstWhere(
        (item) => item.id == transaction.itemId,
        orElse: () => items.isNotEmpty ? items.first : null,
      );
      final isIncoming = transaction.type == TransactionType.incoming;
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = transaction.date.toString().split(' ')[0];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = item?.name ?? transaction.itemName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = isIncoming ? 'وارد' : 'صادر';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = transaction.quantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = item?.quantity ?? 0;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = isIncoming ? (transaction.requestNumber ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = !isIncoming ? (transaction.central ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = !isIncoming ? (transaction.recipientName ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = !isIncoming ? (transaction.spendingReason ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row))
          .value = transaction.responsiblePerson;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row))
          .value = transaction.notes ?? '';
    }
  }

  void _addUsersToSheet(Sheet sheet, List<dynamic> users) {
    // العناوين
    final headers = [
      'اسم المستخدم',
      'الاسم الكامل',
      'الدور',
      'الحالة',
      'تاريخ الإنشاء'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = headers[i];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        backgroundColorHex: '#E3F2FD',
      );
    }

    // البيانات
    for (int i = 0; i < users.length; i++) {
      final user = users[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = user.username;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = user.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = user.roleDisplayName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = user.isActive ? 'نشط' : 'غير نشط';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = user.createdAt.toString().split(' ')[0];
    }
  }

  void _addSummaryToSheet(Sheet sheet, List<dynamic> items,
      List<dynamic> transactions, List<dynamic> users) {
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'ملخص تصدير البيانات';
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2)).value =
        'تاريخ التصدير:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 2)).value =
        DateTime.now().toString().split(' ')[0];

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 4)).value =
        'إجمالي الأصناف:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 4)).value =
        items.length;

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 5)).value =
        'إجمالي العمليات:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 5)).value =
        transactions.length;

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 6)).value =
        'إجمالي المستخدمين:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 6)).value =
        users.length;

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 7)).value =
        'إجمالي الكمية في المخزون:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 7)).value =
        items.fold<double>(0, (sum, item) => sum + item.quantity);
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع البيانات'),
        content: const Text(
            'هل أنت متأكد من حذف جميع البيانات نهائياً؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم مسح جميع البيانات')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _checkForUpdates() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('لا توجد تحديثات جديدة متاحة')),
    );
  }

  void _showUserGuide() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح دليل المستخدم')),
    );
  }

  void _showPrivacyPolicy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح سياسة الخصوصية')),
    );
  }

  void _showTermsOfService() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح شروط الاستخدام')),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح صفحة التواصل مع الدعم الفني')),
    );
  }

  void _showRecalculateInventoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة حساب المخزون'),
        content: const Text(
            'سيتم إعادة حساب كميات جميع الأصناف بناءً على الحركات المسجلة. هل تريد المتابعة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _databaseService.recalculateAllInventory();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إعادة حساب المخزون بنجاح'),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ أثناء إعادة الحساب: $e'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            child: const Text('إعادة الحساب'),
          ),
        ],
      ),
    );
  }
}
