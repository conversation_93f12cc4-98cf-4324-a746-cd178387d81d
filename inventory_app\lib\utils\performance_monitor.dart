import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/material.dart';
import '../core/service_locator.dart';
import '../services/logging_service.dart';

class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  late final LoggingService _loggingService;
  final Map<String, Stopwatch> _activeTimers = {};
  final Map<String, List<Duration>> _performanceHistory = {};
  Timer? _frameRateMonitor;
  int _frameCount = 0;
  DateTime _lastFrameTime = DateTime.now();

  void initialize() {
    _loggingService = serviceLocator<LoggingService>();

    if (kDebugMode) {
      _startFrameRateMonitoring();
    }
  }

  void _startFrameRateMonitoring() {
    _frameRateMonitor = Timer.periodic(const Duration(seconds: 5), (timer) {
      _logFrameRate();
    });

    SchedulerBinding.instance.addPersistentFrameCallback(_onFrame);
  }

  void _onFrame(Duration timestamp) {
    _frameCount++;
  }

  void _logFrameRate() {
    final now = DateTime.now();
    final duration = now.difference(_lastFrameTime);

    // Prevent division by zero and infinity
    if (duration.inSeconds <= 0 || _frameCount <= 0) {
      _frameCount = 0;
      _lastFrameTime = now;
      return;
    }

    final fps = _frameCount / duration.inSeconds;

    // Ensure fps is finite and reasonable
    if (!fps.isFinite || fps <= 0) {
      _frameCount = 0;
      _lastFrameTime = now;
      return;
    }

    final frameTimeMs =
        (1000 / fps).clamp(1.0, 1000.0); // Clamp to reasonable values
    _loggingService.logPerformance(
        'Frame Rate', Duration(milliseconds: frameTimeMs.round()));

    if (fps < 30) {
      _loggingService
          .logWarning('Low frame rate detected: ${fps.toStringAsFixed(1)} FPS');
    }

    _frameCount = 0;
    _lastFrameTime = now;
  }

  // Start timing an operation
  void startTimer(String operationName) {
    _activeTimers[operationName] = Stopwatch()..start();
  }

  // End timing and log the result
  Duration? endTimer(String operationName) {
    final stopwatch = _activeTimers.remove(operationName);
    if (stopwatch == null) {
      _loggingService.logWarning('Timer not found: $operationName');
      return null;
    }

    stopwatch.stop();
    final duration = stopwatch.elapsed;

    // Store in history
    _performanceHistory.putIfAbsent(operationName, () => []).add(duration);

    // Keep only last 100 measurements
    final history = _performanceHistory[operationName]!;
    if (history.length > 100) {
      history.removeAt(0);
    }

    _loggingService.logPerformance(operationName, duration);

    // Log warning for slow operations
    if (duration.inMilliseconds > 1000) {
      _loggingService.logWarning(
          'Slow operation detected: $operationName took ${duration.inMilliseconds}ms');
    }

    return duration;
  }

  // Measure a synchronous operation
  T measureSync<T>(String operationName, T Function() operation) {
    startTimer(operationName);
    try {
      return operation();
    } finally {
      endTimer(operationName);
    }
  }

  // Measure an asynchronous operation
  Future<T> measureAsync<T>(
      String operationName, Future<T> Function() operation) async {
    startTimer(operationName);
    try {
      return await operation();
    } finally {
      endTimer(operationName);
    }
  }

  // Get performance statistics
  Map<String, PerformanceStats> getStatistics() {
    final stats = <String, PerformanceStats>{};

    for (final entry in _performanceHistory.entries) {
      final durations = entry.value;
      if (durations.isEmpty) continue;

      final totalMs =
          durations.fold<int>(0, (sum, d) => sum + d.inMilliseconds);
      final avgMs = durations.isNotEmpty ? totalMs / durations.length : 0.0;
      final minMs = durations
          .map((d) => d.inMilliseconds)
          .reduce((a, b) => a < b ? a : b);
      final maxMs = durations
          .map((d) => d.inMilliseconds)
          .reduce((a, b) => a > b ? a : b);

      stats[entry.key] = PerformanceStats(
        operationName: entry.key,
        count: durations.length,
        averageMs: avgMs,
        minMs: minMs,
        maxMs: maxMs,
        totalMs: totalMs,
      );
    }

    return stats;
  }

  // Print performance report
  void printReport() {
    final stats = getStatistics();

    _loggingService.logInfo('=== Performance Report ===');

    if (stats.isEmpty) {
      _loggingService.logInfo('No performance data available');
      return;
    }

    // Sort by average time (slowest first)
    final sortedStats = stats.values.toList()
      ..sort((a, b) => b.averageMs.compareTo(a.averageMs));

    for (final stat in sortedStats) {
      _loggingService.logInfo('${stat.operationName}: '
          'avg=${stat.averageMs.toStringAsFixed(1)}ms, '
          'min=${stat.minMs}ms, '
          'max=${stat.maxMs}ms, '
          'count=${stat.count}');
    }
  }

  // Clear performance history
  void clearHistory() {
    _performanceHistory.clear();
    _loggingService.logInfo('Performance history cleared');
  }

  // Monitor widget build performance
  void monitorWidgetBuild(String widgetName, VoidCallback buildFunction) {
    if (kDebugMode) {
      measureSync('Widget Build: $widgetName', buildFunction);
    } else {
      buildFunction();
    }
  }

  // Monitor database query performance
  Future<T> monitorDatabaseQuery<T>(
      String queryName, Future<T> Function() query) async {
    return measureAsync('DB Query: $queryName', query);
  }

  // Monitor network request performance
  Future<T> monitorNetworkRequest<T>(
      String requestName, Future<T> Function() request) async {
    return measureAsync('Network: $requestName', request);
  }

  // Monitor cache operations
  T monitorCacheOperation<T>(String operationName, T Function() operation) {
    return measureSync('Cache: $operationName', operation);
  }

  void dispose() {
    _frameRateMonitor?.cancel();
    _activeTimers.clear();
    _performanceHistory.clear();
  }
}

class PerformanceStats {
  final String operationName;
  final int count;
  final double averageMs;
  final int minMs;
  final int maxMs;
  final int totalMs;

  const PerformanceStats({
    required this.operationName,
    required this.count,
    required this.averageMs,
    required this.minMs,
    required this.maxMs,
    required this.totalMs,
  });

  @override
  String toString() {
    return 'PerformanceStats(operation: $operationName, count: $count, avg: ${averageMs.toStringAsFixed(1)}ms)';
  }
}

// Mixin for widgets to easily monitor performance
mixin PerformanceMonitoringMixin<T extends StatefulWidget> on State<T> {
  final PerformanceMonitor _monitor = PerformanceMonitor();

  void startPerformanceTimer(String operationName) {
    _monitor.startTimer('${T.toString()}: $operationName');
  }

  void endPerformanceTimer(String operationName) {
    _monitor.endTimer('${T.toString()}: $operationName');
  }

  Future<R> measureAsyncOperation<R>(
      String operationName, Future<R> Function() operation) {
    return _monitor.measureAsync('${T.toString()}: $operationName', operation);
  }

  R measureSyncOperation<R>(String operationName, R Function() operation) {
    return _monitor.measureSync('${T.toString()}: $operationName', operation);
  }

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      _monitor.startTimer('${T.toString()}: initState');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (kDebugMode) {
      _monitor.endTimer('${T.toString()}: initState');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      return _monitor.measureSync(
          '${T.toString()}: build', () => buildWidget(context));
    } else {
      return buildWidget(context);
    }
  }

  // Override this instead of build() when using the mixin
  Widget buildWidget(BuildContext context);
}

// Performance-aware Future wrapper
class PerformanceFuture<T> {
  final Future<T> _future;
  final String _operationName;
  final PerformanceMonitor _monitor = PerformanceMonitor();

  PerformanceFuture(this._operationName, this._future) {
    _monitor.startTimer(_operationName);
  }

  Future<T> get future => _future.whenComplete(() {
        _monitor.endTimer(_operationName);
      });

  static PerformanceFuture<T> create<T>(
      String operationName, Future<T> future) {
    return PerformanceFuture(operationName, future);
  }
}
