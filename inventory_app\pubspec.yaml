name: inventory_app
description: "نظام إدارة المخزن الذكي لمراقبة مخزون معدات الاتصالات"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.6 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  dropdown_search: ^6.0.2
  flutter_typeahead: ^4.8.0
  pull_to_refresh: ^2.0.0
  syncfusion_flutter_xlsio: ^29.2.11
  universal_html: ^2.0.8
  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.1.0
  shared_preferences: ^2.2.2
  supabase_flutter: ^2.3.4
  fl_chart: ^0.66.2
  excel: ^2.1.0
  path_provider: ^2.1.2
  open_file: ^3.3.2
  intl: 0.20.2
  csv: ^5.1.1
  archive: ^3.4.10
  get_it: ^7.7.0

  # UI & Design
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  
  # State Management & MVVM
  provider: ^6.1.1
  
  # Database & Storage
  path: ^1.8.3
  
  # Charts & Analytics
  uuid: ^4.2.1
  image_picker: ^1.0.4
  
  # Export & Import
  responsive_framework: ^1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  #   - family: PT Sans
  #     fonts:
  #       - asset: assets/fonts/PTSans-Regular.ttf
  #       - asset: assets/fonts/PTSans-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/PTSans-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
