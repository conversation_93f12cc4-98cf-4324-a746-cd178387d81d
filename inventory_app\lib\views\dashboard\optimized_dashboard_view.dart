import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_texts.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';
import '../../core/service_locator.dart';
import '../../repositories/dashboard_repository.dart';
import '../../widgets/app_drawer.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import '../../widgets/common/modern_card.dart';
import '../../widgets/common/empty_state.dart';
import '../../models/transaction.dart';
import 'package:fl_chart/fl_chart.dart';

class OptimizedDashboardView extends StatefulWidget {
  const OptimizedDashboardView({Key? key}) : super(key: key);

  @override
  State<OptimizedDashboardView> createState() => _OptimizedDashboardViewState();
}

class _OptimizedDashboardViewState extends State<OptimizedDashboardView>
    with AutomaticKeepAliveClientMixin {
  late final DashboardRepository _dashboardRepository;

  Map<String, dynamic>? _dashboardStats;
  List<Map<String, dynamic>>? _topUsedItems;
  List<Map<String, dynamic>>? _stockDistribution;

  bool _isLoading = true;
  String? _errorMessage;

  @override
  bool get wantKeepAlive => true; // Keep state alive for better performance

  @override
  void initState() {
    super.initState();
    _dashboardRepository = serviceLocator<DashboardRepository>();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load data concurrently for better performance
      final futures = await Future.wait([
        _dashboardRepository.getDashboardStats(),
        _dashboardRepository.getTopUsedItems(limit: 5),
        _dashboardRepository.getStockDistribution(),
      ]);

      if (mounted) {
        setState(() {
          _dashboardStats = futures[0] as Map<String, dynamic>;
          _topUsedItems = futures[1] as List<Map<String, dynamic>>;
          _stockDistribution = futures[2] as List<Map<String, dynamic>>;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshData() async {
    // Invalidate cache and reload
    _dashboardRepository.invalidateCache();
    await _loadDashboardData();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? AppTexts.dashboard : AppTexts.dashboardEn),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textDark,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshData,
            tooltip: isArabic ? 'تحديث' : 'Refresh',
          ),
        ],
      ),
      drawer: const AppDrawer(currentRoute: '/dashboard'),
      body: _buildBody(context, isArabic),
    );
  }

  Widget _buildBody(BuildContext context, bool isArabic) {
    if (_isLoading) {
      return LoadingWidget(
        message: isArabic ? 'جاري تحميل البيانات...' : 'Loading data...',
      );
    }

    if (_errorMessage != null) {
      return ErrorDisplayWidget(
        message: _errorMessage!,
        onRetry: _loadDashboardData,
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatsCards(context, isArabic),
            const SizedBox(height: 24),
            _buildChartsSection(context, isArabic),
            const SizedBox(height: 24),
            _buildRecentTransactions(context, isArabic),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards(BuildContext context, bool isArabic) {
    if (_dashboardStats == null) return const SizedBox.shrink();

    final stats = _dashboardStats!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الإحصائيات العامة' : 'General Statistics',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final isWideScreen = constraints.maxWidth > 600;
            final crossAxisCount = isWideScreen ? 4 : 2;

            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: isWideScreen ? 1.2 : 1.5,
              children: [
                StatCard(
                  title: isArabic ? 'إجمالي الأصناف' : 'Total Items',
                  value: stats['totalItems']?.toString() ?? '0',
                  icon: Icons.inventory_2,
                  iconColor: AppColors.primary,
                  onTap: () => Navigator.pushNamed(context, '/items'),
                ),
                StatCard(
                  title: isArabic ? 'إجمالي الكميات' : 'Total Quantity',
                  value:
                      (stats['totalQuantity'] as double?)?.toStringAsFixed(0) ??
                          '0',
                  icon: Icons.numbers,
                  iconColor: AppColors.success,
                ),
                StatCard(
                  title: isArabic ? 'مخزون منخفض' : 'Low Stock',
                  value: stats['lowStockItems']?.toString() ?? '0',
                  icon: Icons.warning,
                  iconColor: AppColors.warning,
                  subtitle: isArabic ? 'يحتاج إعادة تخزين' : 'Needs restocking',
                  onTap: () => Navigator.pushNamed(context, '/items'),
                ),
                StatCard(
                  title: isArabic ? 'الفئات' : 'Categories',
                  value: stats['categories']?.toString() ?? '0',
                  icon: Icons.category,
                  iconColor: AppColors.info,
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildChartsSection(BuildContext context, bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'الرسوم البيانية' : 'Charts',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final isWideScreen = constraints.maxWidth > 800;

            if (isWideScreen) {
              return Row(
                children: [
                  Expanded(child: _buildTopUsedItemsChart(context, isArabic)),
                  const SizedBox(width: 16),
                  Expanded(
                      child: _buildStockDistributionChart(context, isArabic)),
                ],
              );
            } else {
              return Column(
                children: [
                  _buildTopUsedItemsChart(context, isArabic),
                  const SizedBox(height: 16),
                  _buildStockDistributionChart(context, isArabic),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildTopUsedItemsChart(BuildContext context, bool isArabic) {
    if (_topUsedItems == null || _topUsedItems!.isEmpty) {
      return _buildEmptyChart(
        context,
        title: isArabic ? 'أكثر الأصناف استخداماً' : 'Most Used Items',
        message: isArabic ? 'لا توجد بيانات' : 'No data available',
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppColors.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'أكثر الأصناف استخداماً' : 'Most Used Items',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: _topUsedItems!.isNotEmpty
                    ? (_topUsedItems!.first['totalUsage'] as double) * 1.2
                    : 100,
                barTouchData: BarTouchData(enabled: true),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < _topUsedItems!.length) {
                          final name = _topUsedItems![index]['name'] as String;
                          return Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              name.length > 8
                                  ? '${name.substring(0, 8)}...'
                                  : name,
                              style: const TextStyle(fontSize: 10),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _topUsedItems!.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return BarChartGroupData(
                    x: index,
                    barRods: [
                      BarChartRodData(
                        toY: (item['totalUsage'] as double),
                        color: AppColors.primary,
                        width: 16,
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(4),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStockDistributionChart(BuildContext context, bool isArabic) {
    if (_stockDistribution == null || _stockDistribution!.isEmpty) {
      return _buildEmptyChart(
        context,
        title: isArabic ? 'توزيع المخزون' : 'Stock Distribution',
        message: isArabic ? 'لا توجد بيانات' : 'No data available',
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppColors.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'توزيع المخزون' : 'Stock Distribution',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _stockDistribution!.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  final colors = [
                    AppColors.primary,
                    AppColors.success,
                    AppColors.warning,
                    AppColors.info,
                    AppColors.error,
                  ];
                  return PieChartSectionData(
                    value: item['percentage'] as double,
                    title:
                        '${(item['percentage'] as double).toStringAsFixed(1)}%',
                    color: colors[index % colors.length],
                    radius: 60,
                    titleStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  );
                }).toList(),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _stockDistribution!.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final colors = [
                AppColors.primary,
                AppColors.success,
                AppColors.warning,
                AppColors.info,
                AppColors.error,
              ];
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: colors[index % colors.length],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    item['category'] as String,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChart(BuildContext context,
      {required String title, required String message}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppColors.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.bar_chart,
                    size: 48,
                    color: AppColors.textLight,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    message,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textLight,
                        ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions(BuildContext context, bool isArabic) {
    if (_dashboardStats == null) return const SizedBox.shrink();

    final recentTransactions =
        _dashboardStats!['recentTransactions'] as List<Transaction>?;

    if (recentTransactions == null || recentTransactions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: AppColors.cardShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'آخر الحركات' : 'Recent Transactions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
            ),
            const SizedBox(height: 16),
            Center(
              child: Text(
                isArabic ? 'لا توجد حركات حديثة' : 'No recent transactions',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textLight,
                    ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppColors.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'آخر الحركات' : 'Recent Transactions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
              ),
              TextButton(
                onPressed: () => Navigator.pushNamed(context, '/transactions'),
                child: Text(isArabic ? 'عرض الكل' : 'View All'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: recentTransactions.length,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final transaction = recentTransactions[index];
              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: transaction.type == TransactionType.incoming
                        ? AppColors.success.withValues(alpha: 0.1)
                        : AppColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    transaction.type == TransactionType.incoming
                        ? Icons.add
                        : Icons.remove,
                    color: transaction.type == TransactionType.incoming
                        ? AppColors.success
                        : AppColors.error,
                    size: 20,
                  ),
                ),
                title: Text(
                  transaction
                      .itemId, // You might want to show item name instead
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                subtitle: Text(
                  transaction.notes ?? '',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textLight,
                      ),
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      transaction.quantity.toString(),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: transaction.type == TransactionType.incoming
                                ? AppColors.success
                                : AppColors.error,
                          ),
                    ),
                    Text(
                      transaction.date.toString().split(' ')[0],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textLight,
                          ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
