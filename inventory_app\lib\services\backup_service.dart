import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'package:excel/excel.dart';
import '../models/item.dart';
import '../models/transaction.dart';
import '../models/user.dart';
import 'database_service.dart';

class BackupService {
  static final DatabaseService _databaseService = DatabaseService();

  /// إنشاء نسخة احتياطية من جميع البيانات
  static Future<String?> createBackup() async {
    try {
      // جلب جميع البيانات
      final items = await _databaseService.getAllItems();
      final transactions = await _databaseService.getAllTransactions();
      final users = await _databaseService.getAllUsers();

      // إنشاء كائن النسخة الاحتياطية
      final backupData = {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'items': items.map((item) => item.toJson()).toList(),
        'transactions': transactions.map((t) => t.toJson()).toList(),
        'users': users.map((user) => user.toJson()).toList(),
      };

      // حفظ كملف JSON
      final jsonBackup = await _saveJsonBackup(backupData);

      // حفظ كملف Excel
      final excelBackup = await _saveExcelBackup(items, transactions, users);

      return jsonBackup; // إرجاع مسار ملف JSON
    } catch (e) {
      print('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      return null;
    }
  }

  /// حفظ النسخة الاحتياطية كملف JSON
  static Future<String?> _saveJsonBackup(
      Map<String, dynamic> backupData) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final fileName =
          'inventory_backup_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${backupDir.path}/$fileName');

      final jsonString = JsonEncoder.withIndent('  ').convert(backupData);
      await file.writeAsString(jsonString, encoding: utf8);

      print('✅ تم حفظ النسخة الاحتياطية JSON: ${file.path}');
      return file.path;
    } catch (e) {
      print('❌ خطأ في حفظ ملف JSON: $e');
      return null;
    }
  }

  /// حفظ النسخة الاحتياطية كملف Excel
  static Future<String?> _saveExcelBackup(List<Item> items,
      List<Transaction> transactions, List<User> users) async {
    try {
      final excel = Excel.createExcel();

      // صفحة الأصناف
      final itemsSheet = excel['الأصناف'];
      _addItemsToSheet(itemsSheet, items);

      // صفحة العمليات
      final transactionsSheet = excel['العمليات'];
      _addTransactionsToSheet(transactionsSheet, transactions, items);

      // صفحة المستخدمين
      final usersSheet = excel['المستخدمين'];
      _addUsersToSheet(usersSheet, users);

      // صفحة الملخص
      final summarySheet = excel['الملخص'];
      _addSummaryToSheet(summarySheet, items, transactions, users);

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final fileName =
          'inventory_backup_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final file = File('${backupDir.path}/$fileName');
      await file.writeAsBytes(excel.encode()!);

      print('✅ تم حفظ النسخة الاحتياطية Excel: ${file.path}');
      return file.path;
    } catch (e) {
      print('❌ خطأ في حفظ ملف Excel: $e');
      return null;
    }
  }

  /// إضافة الأصناف إلى ورقة Excel
  static void _addItemsToSheet(Sheet sheet, List<Item> items) {
    // العناوين
    final headers = [
      'الكود',
      'الاسم',
      'الفئة',
      'الكمية',
      'الوحدة',
      'الحد الأدنى',
      'الحالة',
      'تاريخ الإنشاء',
      'تاريخ التحديث'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = headers[i];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        backgroundColorHex: '#E3F2FD',
      );
    }

    // البيانات
    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = item.code;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = item.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = item.categoryDisplayName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = item.quantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = item.unitDisplayName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = item.minQuantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = item.statusText;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = item.createdAt.toString().split(' ')[0];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = item.updatedAt.toString().split(' ')[0];
    }
  }

  /// إضافة العمليات إلى ورقة Excel
  static void _addTransactionsToSheet(
      Sheet sheet, List<Transaction> transactions, List<Item> items) {
    // العناوين
    final headers = [
      'التاريخ',
      'اسم الصنف',
      'نوع العملية',
      'الكمية',
      'الكمية الحالية',
      'رقم كشف الطلب',
      'السنترال',
      'اسم المنصرف إليه',
      'أسباب الصرف',
      'الشخص المسؤول',
      'الملاحظات'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = headers[i];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        backgroundColorHex: '#E3F2FD',
      );
    }

    // البيانات
    for (int i = 0; i < transactions.length; i++) {
      final transaction = transactions[i];
      final item = items.firstWhere(
        (item) => item.id == transaction.itemId,
        orElse: () => Item(
          id: '0',
          name: 'غير معروف',
          code: '',
          category: ItemCategory.other,
          quantity: 0,
          unit: ItemUnit.piece,
          minQuantity: 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      final isIncoming = transaction.type == TransactionType.incoming;
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = transaction.date.toString().split(' ')[0];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = item.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = isIncoming ? 'وارد' : 'صادر';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = transaction.quantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = item.quantity;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = isIncoming ? (transaction.requestNumber ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
          .value = !isIncoming ? (transaction.central ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
          .value = !isIncoming ? (transaction.recipientName ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
          .value = !isIncoming ? (transaction.spendingReason ?? '') : '';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row))
          .value = transaction.responsiblePerson;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row))
          .value = transaction.notes ?? '';
    }
  }

  /// إضافة المستخدمين إلى ورقة Excel
  static void _addUsersToSheet(Sheet sheet, List<User> users) {
    // العناوين
    final headers = [
      'اسم المستخدم',
      'الاسم الكامل',
      'البريد الإلكتروني',
      'الدور',
      'الحالة',
      'تاريخ الإنشاء'
    ];

    for (int i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = headers[i];
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
        backgroundColorHex: '#E3F2FD',
      );
    }

    // البيانات
    for (int i = 0; i < users.length; i++) {
      final user = users[i];
      final row = i + 1;

      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
          .value = user.username;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
          .value = user.name;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
          .value = user.username; // استخدام username كبديل للبريد الإلكتروني
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
          .value = user.roleDisplayName;
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
          .value = user.isActive ? 'نشط' : 'غير نشط';
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
          .value = user.createdAt.toString().split(' ')[0];
    }
  }

  /// إضافة الملخص إلى ورقة Excel
  static void _addSummaryToSheet(Sheet sheet, List<Item> items,
      List<Transaction> transactions, List<User> users) {
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value =
        'ملخص النسخة الاحتياطية';
    sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0))
        .cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
      horizontalAlign: HorizontalAlign.Center,
    );

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2)).value =
        'تاريخ النسخة الاحتياطية:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 2)).value =
        DateTime.now().toString().split(' ')[0];

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 4)).value =
        'إجمالي الأصناف:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 4)).value =
        items.length;

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 5)).value =
        'إجمالي العمليات:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 5)).value =
        transactions.length;

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 6)).value =
        'إجمالي المستخدمين:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 6)).value =
        users.length;

    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 7)).value =
        'إجمالي الكمية في المخزون:';
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 7)).value =
        items.fold<double>(0, (sum, item) => sum + item.quantity);
  }

  /// استعادة البيانات من ملف JSON
  static Future<bool> restoreFromJson(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      final jsonString = await file.readAsString(encoding: utf8);
      final backupData = json.decode(jsonString) as Map<String, dynamic>;

      // التحقق من إصدار النسخة الاحتياطية
      final version = backupData['version'] as String?;
      if (version == null) {
        throw Exception('إصدار النسخة الاحتياطية غير معروف');
      }

      // استعادة البيانات
      await _restoreData(backupData);

      print('✅ تم استعادة البيانات بنجاح');
      return true;
    } catch (e) {
      print('❌ خطأ في استعادة البيانات: $e');
      return false;
    }
  }

  /// استعادة البيانات من ملف Excel
  static Future<bool> restoreFromExcel(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      // قراءة البيانات من الأوراق
      final items = _readItemsFromSheet(excel['الأصناف']);
      final transactions = _readTransactionsFromSheet(excel['العمليات']);
      final users = _readUsersFromSheet(excel['المستخدمين']);

      // إنشاء كائن النسخة الاحتياطية
      final backupData = {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'items': items.map((item) => item.toJson()).toList(),
        'transactions': transactions.map((t) => t.toJson()).toList(),
        'users': users.map((user) => user.toJson()).toList(),
      };

      // استعادة البيانات
      await _restoreData(backupData);

      print('✅ تم استعادة البيانات من Excel بنجاح');
      return true;
    } catch (e) {
      print('❌ خطأ في استعادة البيانات من Excel: $e');
      return false;
    }
  }

  /// قراءة الأصناف من ورقة Excel
  static List<Item> _readItemsFromSheet(Sheet sheet) {
    final items = <Item>[];

    for (int row = 1; row < sheet.maxRows; row++) {
      try {
        final code = sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
                .value
                ?.toString() ??
            '';
        if (code.isEmpty) continue;

        final item = Item(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          code: code,
          name: sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
                  .value
                  ?.toString() ??
              '',
          category: _parseCategory(sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                  .value
                  ?.toString() ??
              ''),
          quantity: double.tryParse(sheet
                      .cell(CellIndex.indexByColumnRow(
                          columnIndex: 3, rowIndex: row))
                      .value
                      ?.toString() ??
                  '0') ??
              0,
          unit: _parseUnit(sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
                  .value
                  ?.toString() ??
              ''),
          minQuantity: double.tryParse(sheet
                      .cell(CellIndex.indexByColumnRow(
                          columnIndex: 5, rowIndex: row))
                      .value
                      ?.toString() ??
                  '0') ??
              0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        items.add(item);
      } catch (e) {
        print('خطأ في قراءة صف الصنف $row: $e');
      }
    }

    return items;
  }

  /// قراءة العمليات من ورقة Excel
  static List<Transaction> _readTransactionsFromSheet(Sheet sheet) {
    final transactions = <Transaction>[];

    for (int row = 1; row < sheet.maxRows; row++) {
      try {
        final dateStr = sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
                .value
                ?.toString() ??
            '';
        if (dateStr.isEmpty) continue;

        final transaction = Transaction(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          itemId: '0', // سيتم تحديثه لاحقاً
          itemName: sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
                  .value
                  ?.toString() ??
              '',
          type: _parseTransactionType(sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row))
                  .value
                  ?.toString() ??
              ''),
          quantity: double.tryParse(sheet
                      .cell(CellIndex.indexByColumnRow(
                          columnIndex: 3, rowIndex: row))
                      .value
                      ?.toString() ??
                  '0') ??
              0,
          unit: sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
                  .value
                  ?.toString() ??
              '',
          date: DateTime.tryParse(dateStr) ?? DateTime.now(),
          requestNumber: sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row))
              .value
              ?.toString(),
          central: sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row))
              .value
              ?.toString(),
          recipientName: sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: 7, rowIndex: row))
              .value
              ?.toString(),
          spendingReason: sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: 8, rowIndex: row))
              .value
              ?.toString(),
          responsiblePerson: sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 9, rowIndex: row))
                  .value
                  ?.toString() ??
              '',
          notes: sheet
              .cell(CellIndex.indexByColumnRow(columnIndex: 10, rowIndex: row))
              .value
              ?.toString(),
          createdBy: 'system', // المستخدم الافتراضي
          createdAt: DateTime.now(),
        );

        transactions.add(transaction);
      } catch (e) {
        print('خطأ في قراءة صف العملية $row: $e');
      }
    }

    return transactions;
  }

  /// قراءة المستخدمين من ورقة Excel
  static List<User> _readUsersFromSheet(Sheet sheet) {
    final users = <User>[];

    for (int row = 1; row < sheet.maxRows; row++) {
      try {
        final username = sheet
                .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row))
                .value
                ?.toString() ??
            '';
        if (username.isEmpty) continue;

        final user = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row))
                  .value
                  ?.toString() ??
              '',
          username: username,
          password: 'password123', // كلمة مرور افتراضية
          role: _parseUserRole(sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row))
                  .value
                  ?.toString() ??
              ''),
          createdAt: DateTime.now(),
          isActive: sheet
                  .cell(
                      CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row))
                  .value
                  ?.toString() ==
              'نشط',
        );

        users.add(user);
      } catch (e) {
        print('خطأ في قراءة صف المستخدم $row: $e');
      }
    }

    return users;
  }

  /// استعادة البيانات إلى قاعدة البيانات
  static Future<void> _restoreData(Map<String, dynamic> backupData) async {
    try {
      // حذف البيانات الحالية
      await _clearCurrentData();

      // استعادة الأصناف
      final items = (backupData['items'] as List<dynamic>)
          .map((json) => Item.fromJson(json))
          .toList();

      for (final item in items) {
        await _databaseService.insertItem(item);
      }

      // استعادة العمليات
      final transactions = (backupData['transactions'] as List<dynamic>)
          .map((json) => Transaction.fromJson(json))
          .toList();

      for (final transaction in transactions) {
        await _databaseService.insertTransaction(transaction);
      }

      // استعادة المستخدمين
      final users = (backupData['users'] as List<dynamic>)
          .map((json) => User.fromJson(json))
          .toList();

      for (final user in users) {
        await _databaseService.insertUser(user);
      }

      print(
          '✅ تم استعادة ${items.length} صنف، ${transactions.length} عملية، ${users.length} مستخدم');
    } catch (e) {
      print('❌ خطأ في استعادة البيانات: $e');
      rethrow;
    }
  }

  /// حذف البيانات الحالية
  static Future<void> _clearCurrentData() async {
    try {
      // حذف جميع البيانات من الجداول
      await _databaseService.supabase
          .from('transactions')
          .delete()
          .neq('id', '0');
      await _databaseService.supabase.from('items').delete().neq('id', '0');
      await _databaseService.supabase.from('users').delete().neq('id', '0');

      print('✅ تم حذف البيانات الحالية');
    } catch (e) {
      print('❌ خطأ في حذف البيانات الحالية: $e');
      rethrow;
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية المتاحة
  static Future<List<File>> getAvailableBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');

      if (!await backupDir.exists()) {
        return [];
      }

      final files = await backupDir.list().toList();
      return files
          .whereType<File>()
          .where((file) =>
              file.path.endsWith('.json') || file.path.endsWith('.xlsx'))
          .toList();
    } catch (e) {
      print('❌ خطأ في الحصول على النسخ الاحتياطية: $e');
      return [];
    }
  }

  /// فتح ملف النسخة الاحتياطية
  static Future<void> openBackupFile(String filePath) async {
    try {
      await OpenFile.open(filePath);
    } catch (e) {
      print('❌ خطأ في فتح الملف: $e');
    }
  }

  // دوال مساعدة لتحليل البيانات
  static ItemCategory _parseCategory(String category) {
    switch (category) {
      case 'كابل نحاس':
        return ItemCategory.copperCable;
      case 'كابل فايبر':
        return ItemCategory.fiberCable;
      case 'لحام نحاس':
        return ItemCategory.copperWelding;
      case 'لحام فايبر':
        return ItemCategory.fiberWelding;
      case 'أخرى':
        return ItemCategory.other;
      default:
        return ItemCategory.other;
    }
  }

  static ItemUnit _parseUnit(String unit) {
    switch (unit) {
      case 'قطعة':
        return ItemUnit.piece;
      case 'كيلوغرام':
        return ItemUnit.kg;
      case 'لتر':
        return ItemUnit.liter;
      case 'متر':
        return ItemUnit.meter;
      case 'صندوق':
        return ItemUnit.box;
      default:
        return ItemUnit.piece;
    }
  }

  static TransactionType _parseTransactionType(String type) {
    switch (type) {
      case 'وارد':
        return TransactionType.incoming;
      case 'صادر':
        return TransactionType.outgoing;
      default:
        return TransactionType.incoming;
    }
  }

  static UserRole _parseUserRole(String role) {
    switch (role) {
      case 'مدير':
        return UserRole.admin;
      case 'مشرف':
        return UserRole.manager;
      case 'محاسب':
        return UserRole.accountant;
      case 'مستخدم':
        return UserRole.user;
      default:
        return UserRole.user;
    }
  }
}
