import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

class MemoryManager {
  static final MemoryManager _instance = MemoryManager._internal();
  factory MemoryManager() => _instance;
  MemoryManager._internal();

  final List<StreamSubscription> _subscriptions = [];
  final List<Timer> _timers = [];
  final Set<ChangeNotifier> _notifiers = {};
  final Map<String, dynamic> _resources = {};

  // Track memory usage
  Timer? _memoryMonitorTimer;

  void initialize() {
    _startMemoryMonitoring();
  }

  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkMemoryUsage();
    });
  }

  Future<void> _checkMemoryUsage() async {
    if (kDebugMode) {
      try {
        // Get current memory usage (this is a simplified approach)
        await SystemChannels.platform
            .invokeMethod('SystemNavigator.routeUpdated');
        // In a real implementation, you'd use platform-specific methods to get actual memory usage

        developer.log('Memory check performed', name: 'MemoryManager');

        // Trigger garbage collection if memory usage is high
        if (_shouldTriggerGC()) {
          _triggerGarbageCollection();
        }
      } catch (e) {
        developer.log('Error checking memory usage: $e', name: 'MemoryManager');
      }
    }
  }

  bool _shouldTriggerGC() {
    // Simple heuristic - in a real app, you'd use actual memory metrics
    return _subscriptions.length > 50 || _notifiers.length > 20;
  }

  void _triggerGarbageCollection() {
    developer.log('Triggering garbage collection', name: 'MemoryManager');

    // Clean up expired resources
    _cleanupExpiredResources();

    // Force garbage collection (use sparingly)
    if (kDebugMode) {
      // Note: There's no direct way to force GC in Dart, but we can help by nullifying references
      _cleanupWeakReferences();
    }
  }

  void _cleanupExpiredResources() {
    final now = DateTime.now();
    _resources.removeWhere((key, value) {
      if (value is Map && value.containsKey('expiry')) {
        final expiry = value['expiry'] as DateTime;
        return now.isAfter(expiry);
      }
      return false;
    });
  }

  void _cleanupWeakReferences() {
    // Remove disposed notifiers
    _notifiers.removeWhere((notifier) => !notifier.hasListeners);

    // Cancel completed subscriptions
    _subscriptions.removeWhere((subscription) => subscription.isPaused);
  }

  // Resource management methods
  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  void addTimer(Timer timer) {
    _timers.add(timer);
  }

  void addNotifier(ChangeNotifier notifier) {
    _notifiers.add(notifier);
  }

  void addResource(String key, dynamic resource, {Duration? expiry}) {
    _resources[key] = {
      'resource': resource,
      'expiry': expiry != null ? DateTime.now().add(expiry) : null,
      'created': DateTime.now(),
    };
  }

  T? getResource<T>(String key) {
    final entry = _resources[key];
    if (entry == null) return null;

    // Check if expired
    final expiry = entry['expiry'] as DateTime?;
    if (expiry != null && DateTime.now().isAfter(expiry)) {
      _resources.remove(key);
      return null;
    }

    return entry['resource'] as T?;
  }

  void removeResource(String key) {
    final entry = _resources.remove(key);
    if (entry != null) {
      final resource = entry['resource'];
      if (resource is ChangeNotifier) {
        resource.dispose();
      }
    }
  }

  // Cleanup methods
  void disposeSubscription(StreamSubscription subscription) {
    subscription.cancel();
    _subscriptions.remove(subscription);
  }

  void disposeTimer(Timer timer) {
    timer.cancel();
    _timers.remove(timer);
  }

  void disposeNotifier(ChangeNotifier notifier) {
    notifier.dispose();
    _notifiers.remove(notifier);
  }

  void disposeAll() {
    // Cancel all subscriptions
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();

    // Cancel all timers
    for (final timer in _timers) {
      timer.cancel();
    }
    _timers.clear();

    // Dispose all notifiers
    for (final notifier in _notifiers) {
      try {
        notifier.dispose();
      } catch (e) {
        developer.log('Error disposing notifier: $e', name: 'MemoryManager');
      }
    }
    _notifiers.clear();

    // Clear all resources
    for (final entry in _resources.values) {
      final resource = entry['resource'];
      if (resource is ChangeNotifier) {
        try {
          resource.dispose();
        } catch (e) {
          developer.log('Error disposing resource: $e', name: 'MemoryManager');
        }
      }
    }
    _resources.clear();

    // Cancel memory monitoring
    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;

    developer.log('All resources disposed', name: 'MemoryManager');
  }

  // Memory optimization utilities
  void optimizeMemory() {
    _cleanupExpiredResources();
    _cleanupWeakReferences();

    // Suggest garbage collection
    if (kDebugMode) {
      developer.log('Memory optimization completed', name: 'MemoryManager');
    }
  }

  // Statistics
  Map<String, int> getStatistics() {
    return {
      'subscriptions': _subscriptions.length,
      'timers': _timers.length,
      'notifiers': _notifiers.length,
      'resources': _resources.length,
    };
  }

  void printStatistics() {
    final stats = getStatistics();
    developer.log('Memory Manager Statistics:', name: 'MemoryManager');
    developer.log('  Subscriptions: ${stats['subscriptions']}',
        name: 'MemoryManager');
    developer.log('  Timers: ${stats['timers']}', name: 'MemoryManager');
    developer.log('  Notifiers: ${stats['notifiers']}', name: 'MemoryManager');
    developer.log('  Resources: ${stats['resources']}', name: 'MemoryManager');
  }
}

// Mixin for automatic memory management in StatefulWidgets
mixin AutoMemoryManagement<T extends StatefulWidget> on State<T> {
  final List<StreamSubscription> _subscriptions = [];
  final List<Timer> _timers = [];
  final List<ChangeNotifier> _notifiers = [];

  void addSubscription(StreamSubscription subscription) {
    _subscriptions.add(subscription);
    MemoryManager().addSubscription(subscription);
  }

  void addTimer(Timer timer) {
    _timers.add(timer);
    MemoryManager().addTimer(timer);
  }

  void addNotifier(ChangeNotifier notifier) {
    _notifiers.add(notifier);
    MemoryManager().addNotifier(notifier);
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }

    // Cancel all timers
    for (final timer in _timers) {
      timer.cancel();
    }

    // Dispose all notifiers
    for (final notifier in _notifiers) {
      try {
        notifier.dispose();
      } catch (e) {
        developer.log('Error disposing notifier in ${T.toString()}: $e');
      }
    }

    super.dispose();
  }
}

// Utility class for managing image memory
class ImageMemoryManager {
  static final Map<String, DateTime> _imageCache = {};
  static const Duration _cacheExpiry = Duration(minutes: 30);

  static void trackImage(String imageUrl) {
    _imageCache[imageUrl] = DateTime.now();
  }

  static void clearExpiredImages() {
    final now = DateTime.now();
    _imageCache.removeWhere((url, timestamp) {
      return now.difference(timestamp) > _cacheExpiry;
    });
  }

  static void clearAllImages() {
    _imageCache.clear();
    // Clear Flutter's image cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  static int getCachedImageCount() {
    return _imageCache.length;
  }
}

// Utility for managing large lists and pagination
class ListMemoryManager {
  static const int _maxItemsInMemory = 1000;

  static List<T> optimizeList<T>(List<T> items) {
    if (items.length <= _maxItemsInMemory) {
      return items;
    }

    // Keep only the most recent items
    return items.take(_maxItemsInMemory).toList();
  }

  static void clearListCache() {
    // Implementation depends on your specific list caching strategy
    developer.log('List cache cleared', name: 'ListMemoryManager');
  }
}
