import '../models/transaction.dart';
import '../services/database_service.dart';
import '../services/cache_service.dart';
import '../services/logging_service.dart';

class DashboardRepository {
  final DatabaseService _databaseService;
  final CacheService _cacheService;
  final LoggingService _loggingService;

  DashboardRepository(
      this._databaseService, this._cacheService, this._loggingService);

  Future<Map<String, dynamic>> getDashboardStats() async {
    final stopwatch = Stopwatch()..start();

    try {
      // Try to get from cache first
      final cachedStats = _cacheService
          .getMemoryCache<Map<String, dynamic>>(CacheKeys.dashboardStats);
      if (cachedStats != null) {
        _loggingService.logCacheHit(CacheKeys.dashboardStats);
        return cachedStats;
      }

      _loggingService.logCacheMiss(CacheKeys.dashboardStats);

      // Use optimized database queries with aggregation
      final stats = await _getOptimizedDashboardStats();

      // Cache the results
      _cacheService.setMemoryCache(
        CacheKeys.dashboardStats,
        stats,
        duration: _cacheService.getCacheDuration(CacheType.dashboard),
      );

      _loggingService.logPerformance('getDashboardStats', stopwatch.elapsed);
      return stats;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get dashboard stats',
          error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      stopwatch.stop();
    }
  }

  Future<Map<String, dynamic>> _getOptimizedDashboardStats() async {
    // Use database aggregation functions for better performance
    final supabase = _databaseService.supabase;

    // Single query to get all item statistics
    final itemStatsQuery = await supabase
        .from('items')
        .select('quantity, min_quantity, category')
        .eq('is_active', true);

    int totalItems = itemStatsQuery.length;
    double totalQuantity = 0.0;
    int lowStockItems = 0;
    Set<String> categories = {};

    for (final item in itemStatsQuery) {
      final quantity = (item['quantity'] as num).toDouble();
      final minQuantity = (item['min_quantity'] as num).toDouble();
      final category = item['category'] as String? ?? 'other';

      totalQuantity += quantity;
      categories.add(category);

      if (quantity <= minQuantity) {
        lowStockItems++;
      }
    }

    // Get recent transactions in a separate optimized query
    final recentTransactionsResult = await supabase
        .from('transactions')
        .select('id, item_id, type, quantity, date, notes')
        .order('date', ascending: false)
        .limit(5);

    final recentTransactions = recentTransactionsResult
        .map((json) => Transaction.fromJson(json))
        .toList();

    return {
      'totalItems': totalItems,
      'totalQuantity': totalQuantity,
      'lowStockItems': lowStockItems,
      'categories': categories.length,
      'recentTransactions': recentTransactions,
    };
  }

  Future<List<Map<String, dynamic>>> getTopUsedItems({int limit = 10}) async {
    final stopwatch = Stopwatch()..start();

    try {
      final cacheKey = '${CacheKeys.topUsedItems}_$limit';
      final cachedItems =
          _cacheService.getMemoryCache<List<Map<String, dynamic>>>(cacheKey);

      if (cachedItems != null) {
        _loggingService.logCacheHit(cacheKey);
        return cachedItems;
      }

      _loggingService.logCacheMiss(cacheKey);

      // Optimized query using database aggregation
      final result = await _getOptimizedTopUsedItems(limit);

      _cacheService.setMemoryCache(
        cacheKey,
        result,
        duration: _cacheService.getCacheDuration(CacheType.dashboard),
      );

      _loggingService.logPerformance('getTopUsedItems', stopwatch.elapsed);
      return result;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get top used items',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<List<Map<String, dynamic>>> _getOptimizedTopUsedItems(
      int limit) async {
    final supabase = _databaseService.supabase;

    // Use database aggregation to calculate usage directly
    final result =
        await supabase.rpc('get_top_used_items', params: {'item_limit': limit});

    if (result == null) {
      // Fallback to application-level aggregation if RPC is not available
      return await _fallbackTopUsedItems(limit);
    }

    return List<Map<String, dynamic>>.from(result);
  }

  Future<List<Map<String, dynamic>>> _fallbackTopUsedItems(int limit) async {
    final supabase = _databaseService.supabase;

    // Get all outgoing transactions grouped by item
    final transactions = await supabase
        .from('transactions')
        .select('item_id, quantity')
        .eq('type', 'outgoing');

    // Aggregate usage by item
    Map<String, double> itemUsage = {};
    for (final transaction in transactions) {
      final itemId = transaction['item_id'] as String;
      final quantity = (transaction['quantity'] as num).toDouble();
      itemUsage[itemId] = (itemUsage[itemId] ?? 0.0) + quantity;
    }

    // Get item details for top used items
    final sortedItems = itemUsage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topItemIds = sortedItems.take(limit).map((e) => e.key).toList();

    if (topItemIds.isEmpty) return [];

    final items = await supabase
        .from('items')
        .select('id, name, code, category')
        .inFilter('id', topItemIds);

    // Combine item details with usage data
    return items.map((item) {
      final usage = itemUsage[item['id']] ?? 0.0;
      return {
        'id': item['id'],
        'name': item['name'],
        'code': item['code'],
        'category': item['category'],
        'totalUsage': usage,
      };
    }).toList()
      ..sort((a, b) =>
          (b['totalUsage'] as double).compareTo(a['totalUsage'] as double));
  }

  Future<List<Map<String, dynamic>>> getStockDistribution() async {
    final stopwatch = Stopwatch()..start();

    try {
      final cachedDistribution =
          _cacheService.getMemoryCache<List<Map<String, dynamic>>>(
              CacheKeys.stockDistribution);

      if (cachedDistribution != null) {
        _loggingService.logCacheHit(CacheKeys.stockDistribution);
        return cachedDistribution;
      }

      _loggingService.logCacheMiss(CacheKeys.stockDistribution);

      final result = await _getOptimizedStockDistribution();

      _cacheService.setMemoryCache(
        CacheKeys.stockDistribution,
        result,
        duration: _cacheService.getCacheDuration(CacheType.dashboard),
      );

      _loggingService.logPerformance('getStockDistribution', stopwatch.elapsed);
      return result;
    } catch (e, stackTrace) {
      _loggingService.logError('Failed to get stock distribution',
          error: e, stackTrace: stackTrace);
      return [];
    } finally {
      stopwatch.stop();
    }
  }

  Future<List<Map<String, dynamic>>> _getOptimizedStockDistribution() async {
    final supabase = _databaseService.supabase;

    // Single query to get category distribution
    final items = await supabase
        .from('items')
        .select('category, quantity')
        .eq('is_active', true);

    Map<String, double> categoryQuantities = {};
    double totalQuantity = 0.0;

    for (final item in items) {
      final category = item['category'] as String? ?? 'other';
      final quantity = (item['quantity'] as num).toDouble();

      categoryQuantities[category] =
          (categoryQuantities[category] ?? 0.0) + quantity;
      totalQuantity += quantity;
    }

    // Calculate percentages and return formatted data
    return categoryQuantities.entries.map((entry) {
      final percentage =
          totalQuantity > 0 ? (entry.value / totalQuantity) * 100 : 0.0;
      return {
        'category': entry.key,
        'quantity': entry.value,
        'percentage': percentage,
      };
    }).toList()
      ..sort((a, b) =>
          (b['quantity'] as double).compareTo(a['quantity'] as double));
  }

  void invalidateCache() {
    _cacheService.invalidateCache('dashboard');
    _cacheService.invalidateCache('top_used');
    _cacheService.invalidateCache('stock_distribution');
    _loggingService.logInfo('Dashboard cache invalidated');
  }
}
