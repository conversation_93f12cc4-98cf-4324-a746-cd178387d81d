import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../constants/app_texts.dart';
import '../../constants/app_colors.dart';
import '../../models/transaction.dart';
import '../../models/item.dart';
import '../../services/database_service.dart';
import '../../widgets/app_drawer.dart';

class TransactionListView extends StatefulWidget {
  const TransactionListView({super.key});

  @override
  State<TransactionListView> createState() => _TransactionListViewState();
}

class _TransactionListViewState extends State<TransactionListView> {
  final DatabaseService _databaseService = DatabaseService();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  List<Transaction> _transactions = [];
  List<Transaction> _filteredTransactions = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  List<Item> _allItems = [];
  List<Item> _filteredItems = [];
  String _itemSearchQuery = '';
  bool _isLoadingItems = true;

  String selectedItemId = '';
  String selectedItemName = '';
  double currentQuantity = 0;
  String currentUnit = '';
  TransactionType type = TransactionType.incoming;
  double quantity = 0;
  String responsiblePerson = '';
  String notes = '';
  String location = '';
  String category = '';
  String spendingReason = '';
  String central = '';
  String recipientName = '';
  String requestNumber = '';
  DateTime selectedDate = DateTime.now();

  final List<String> centralOptions = [
    'العريش',
    'السلام',
    'المساعيد',
    'بئر العبد',
    'الشيخ زويد'
  ];

  final TextEditingController quantityController = TextEditingController();
  final TextEditingController responsibleController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController requestNumberController = TextEditingController();
  final TextEditingController centralController = TextEditingController();
  final TextEditingController recipientNameController = TextEditingController();
  final TextEditingController spendingReasonController =
      TextEditingController();
  final TextEditingController locationController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData({bool isRefresh = false}) async {
    if (!isRefresh) setState(() => _isLoading = true);
    try {
      final transactions = await _databaseService.getAllTransactions();
      final items = await _databaseService.getAllItems();
      setState(() {
        _transactions = transactions;
        _filteredTransactions = transactions;
        _allItems = items;
        _filteredItems = items;
        _isLoading = false;
        _isLoadingItems = false;
      });
      if (isRefresh) _refreshController.refreshCompleted();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      if (isRefresh) _refreshController.refreshFailed();
      setState(() => _isLoading = false);
    }
  }

  void _filterTransactions() {
    setState(() {
      _filteredTransactions = _transactions.where((transaction) {
        return transaction.itemName
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            (transaction.notes
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            transaction.id.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    });
  }

  void _filterItems(String query) {
    setState(() {
      _itemSearchQuery = query;
      _filteredItems = query.isEmpty
          ? _allItems
          : _allItems.where((item) {
              return item.name.toLowerCase().contains(query.toLowerCase()) ||
                  item.code.toLowerCase().contains(query.toLowerCase()) ||
                  item.categoryDisplayName
                      .toLowerCase()
                      .contains(query.toLowerCase());
            }).toList();
    });
  }

  String _getTransactionTypeText(TransactionType type) {
    switch (type) {
      case TransactionType.incoming:
        return 'وارد';
      case TransactionType.outgoing:
        return 'صادر';
    }
  }

  Color _getTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.incoming:
        return AppColors.incoming;
      case TransactionType.outgoing:
        return AppColors.outgoing;
    }
  }

  IconData _getTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.incoming:
        return Icons.arrow_downward;
      case TransactionType.outgoing:
        return Icons.arrow_upward;
    }
  }

  void _showAddTransactionDialog() {
    if (_isLoadingItems || _allItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('يجب تحميل الأصناف أولاً قبل إضافة حركة جديدة')),
      );
      return;
    }

    final _formKey = GlobalKey<FormState>();

    // عند فتح النافذة، عيّن القيم الحالية للـ controllers
    quantityController.text = quantity > 0 ? quantity.toString() : '';
    responsibleController.text = responsiblePerson;
    notesController.text = notes;
    requestNumberController.text = requestNumber;
    centralController.text = central;
    recipientNameController.text = recipientName;
    spendingReasonController.text = spendingReason;
    locationController.text = location;

    void fillWithLastTransaction() {
      if (_transactions.isEmpty) return;
      final last = _transactions.last;
      setState(() {
        selectedItemId = last.itemId;
        selectedItemName = last.itemName;
        category = last.category ?? '';
        final item = _allItems.firstWhere(
          (item) => item.id == last.itemId,
          orElse: () => _allItems.first,
        );
        currentQuantity = item.quantity;
        currentUnit = last.unit;
        type = last.type;
        quantity = last.quantity;
        responsiblePerson = last.responsiblePerson;
        notes = last.notes ?? '';
        location = last.location ?? '';
        requestNumber = last.requestNumber ?? '';
        central = last.central ?? '';
        recipientName = last.recipientName ?? '';
        spendingReason = last.spendingReason ?? '';
        selectedDate = last.date;

        // تحديث القيم في الـ controllers
        quantityController.text = last.quantity.toString();
        responsibleController.text = last.responsiblePerson;
        notesController.text = last.notes ?? '';
        requestNumberController.text = last.requestNumber ?? '';
        centralController.text = last.central ?? '';
        recipientNameController.text = last.recipientName ?? '';
        spendingReasonController.text = last.spendingReason ?? '';
        locationController.text = last.location ?? '';
      });
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('إضافة حركة جديدة'),
          content: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Item selection
                  _isLoadingItems
                      ? const Center(child: CircularProgressIndicator())
                      : DropdownSearch<Item>(
                          items: (filter, props) => _allItems,
                          itemAsString: (Item? item) => item == null
                              ? ''
                              : '${item.name} (${item.code}) - ${item.quantity} ${item.unitDisplayName}',
                          compareFn: (Item a, Item b) => a.id == b.id,
                          onChanged: (Item? item) {
                            if (item != null) {
                              setState(() {
                                selectedItemId = item.id;
                                selectedItemName = item.name;
                                category = item.categoryDisplayName;
                                currentQuantity = item.quantity;
                                currentUnit = item.unitDisplayName;
                              });
                            }
                          },
                          validator: (Item? item) =>
                              item == null ? 'الرجاء اختيار صنف' : null,
                          decoratorProps: DropDownDecoratorProps(
                            decoration: InputDecoration(
                              labelText: 'الصنف',
                              prefixIcon: Icon(Icons.inventory),
                              border: OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(12)),
                              ),
                            ),
                          ),
                          popupProps: PopupProps.dialog(
                            showSearchBox: true,
                            searchFieldProps: TextFieldProps(
                              decoration: InputDecoration(
                                hintText: 'ابحث عن الصنف...',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ),

                  // Selected item info
                  if (selectedItemId.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: AppColors.primary.withOpacity(0.3)),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Icon(Icons.inventory,
                                  color: AppColors.primary, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'معلومات الصنف المختار',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('الكمية الحالية:',
                                  style:
                                      TextStyle(fontWeight: FontWeight.w500)),
                              Text(
                                '$currentQuantity $currentUnit',
                                style: const TextStyle(
                                  color: AppColors.success,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          if (category.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('الفئة:',
                                    style:
                                        TextStyle(fontWeight: FontWeight.w500)),
                                Text(
                                  category,
                                  style: const TextStyle(
                                    color: AppColors.info,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],

                  const SizedBox(height: 16),

                  // Transaction type
                  DropdownButtonFormField<TransactionType>(
                    value: type,
                    decoration: const InputDecoration(
                      labelText: 'نوع الحركة',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                    ),
                    items: TransactionType.values
                        .map((t) => DropdownMenuItem(
                              value: t,
                              child: Text(_getTransactionTypeText(t)),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() => type = value!);
                    },
                    validator: (value) =>
                        value == null ? 'الرجاء اختيار نوع الحركة' : null,
                  ),

                  const SizedBox(height: 16),

                  // Date picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null && mounted) {
                        setState(() => selectedDate = picked);
                      }
                    },
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الحركة',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(_formatDate(selectedDate)),
                          const Icon(Icons.calendar_today),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Quantity
                  TextFormField(
                    controller: quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                    ),
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    validator: (v) {
                      if (v == null || v.isEmpty) return 'الرجاء إدخال الكمية';
                      final value = double.tryParse(v);
                      if (value == null || value <= 0)
                        return 'الكمية يجب أن تكون أكبر من 0';
                      if (type == TransactionType.outgoing &&
                          value > currentQuantity) {
                        return 'الكمية تتجاوز المخزون المتاح';
                      }
                      return null;
                    },
                    onChanged: (v) =>
                        setState(() => quantity = double.tryParse(v) ?? 0),
                  ),

                  const SizedBox(height: 16),

                  // Request number (for incoming)
                  if (type == TransactionType.incoming) ...[
                    TextFormField(
                      controller: requestNumberController,
                      decoration: const InputDecoration(
                        labelText: 'رقم كشف الطلب',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                      ),
                      validator: (v) =>
                          v == null || v.isEmpty ? 'مطلوب للوارد' : null,
                      onChanged: (v) => setState(() => requestNumber = v),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Responsible person
                  TextFormField(
                    controller: responsibleController,
                    decoration: const InputDecoration(
                      labelText: 'الشخص المسؤول',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                    ),
                    validator: (v) => v == null || v.isEmpty ? 'مطلوب' : null,
                    onChanged: (v) => setState(() => responsiblePerson = v),
                  ),

                  const SizedBox(height: 16),

                  // Outgoing specific fields
                  if (type == TransactionType.outgoing) ...[
                    DropdownButtonFormField<String>(
                      value: central.isEmpty ? null : central,
                      decoration: const InputDecoration(
                        labelText: 'السنترال',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                      ),
                      items: centralOptions
                          .map(
                              (c) => DropdownMenuItem(value: c, child: Text(c)))
                          .toList(),
                      onChanged: (value) =>
                          setState(() => central = value ?? ''),
                      validator: (v) =>
                          v == null || v.isEmpty ? 'مطلوب للصادر' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: recipientNameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المنصرف إليه',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                      ),
                      validator: (v) =>
                          v == null || v.isEmpty ? 'مطلوب للصادر' : null,
                      onChanged: (v) => setState(() => recipientName = v),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: spendingReasonController,
                      decoration: const InputDecoration(
                        labelText: 'أسباب الصرف',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                        ),
                      ),
                      maxLines: 2,
                      validator: (v) =>
                          v == null || v.isEmpty ? 'مطلوب للصادر' : null,
                      onChanged: (v) => setState(() => spendingReason = v),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Notes
                  TextFormField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'الملاحظات',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.all(Radius.circular(12)),
                      ),
                    ),
                    maxLines: 3,
                    onChanged: (v) => setState(() => notes = v),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton.icon(
              icon: const Icon(Icons.copy),
              label: const Text('نسخ آخر عملية'),
              onPressed: fillWithLastTransaction,
            ),
            ElevatedButton(
              onPressed: () async {
                if (_formKey.currentState!.validate() &&
                    selectedItemId.isNotEmpty) {
                  final confirmed = await _showConfirmationDialog(
                    title: 'تأكيد الحركة',
                    content: 'هل أنت متأكد من إضافة هذه الحركة؟',
                  );
                  if (!confirmed || !mounted) return;

                  try {
                    final transactionData = {
                      'item_id': selectedItemId,
                      'item_name': selectedItemName,
                      'type': type.toString().split('.').last,
                      'quantity': quantity,
                      'unit': currentUnit,
                      'date': selectedDate.toIso8601String(),
                      'responsible_person': responsiblePerson,
                      'location': location.isNotEmpty ? location : null,
                      'notes': notes.isNotEmpty ? notes : null,
                      'request_number':
                          requestNumber.isNotEmpty ? requestNumber : null,
                      'created_by': 'admin',
                      'category': category.isNotEmpty ? category : null,
                      'spending_reason':
                          spendingReason.isNotEmpty ? spendingReason : null,
                      'central': central.isNotEmpty ? central : null,
                      'recipient_name':
                          recipientName.isNotEmpty ? recipientName : null,
                    };

                    await _databaseService.supabase
                        .from('transactions')
                        .insert(transactionData);

                    final currentItem = _allItems
                        .firstWhere((item) => item.id == selectedItemId);
                    double newQuantity = type == TransactionType.incoming
                        ? currentItem.quantity + quantity
                        : (currentItem.quantity - quantity)
                            .clamp(0, double.infinity);

                    await _databaseService.supabase.from('items').update({
                      'quantity': newQuantity,
                      'updated_at': DateTime.now().toIso8601String(),
                    }).eq('id', selectedItemId);

                    await _loadData();
                    if (mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('تمت إضافة الحركة بنجاح وتحديث المخزون'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('حدث خطأ أثناء إضافة الحركة: $e'),
                          backgroundColor: AppColors.error,
                        ),
                      );
                    }
                  }
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _showConfirmationDialog(
      {required String title, required String content}) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('تأكيد'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _deleteTransaction(Transaction transaction) async {
    final confirmed = await _showConfirmationDialog(
      title: 'تأكيد الحذف',
      content:
          'هل أنت متأكد من حذف هذه الحركة؟ لا يمكن التراجع عن هذا الإجراء.',
    );
    if (!confirmed || !mounted) return;

    try {
      await _databaseService.supabase
          .from('transactions')
          .delete()
          .eq('id', transaction.id);

      final currentItem =
          _allItems.firstWhere((item) => item.id == transaction.itemId);
      double newQuantity = transaction.type == TransactionType.incoming
          ? (currentItem.quantity - transaction.quantity)
              .clamp(0, double.infinity)
          : currentItem.quantity + transaction.quantity;

      await _databaseService.supabase.from('items').update({
        'quantity': newQuantity,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', transaction.itemId);

      await _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الحركة بنجاح وتحديث المخزون'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء حذف الحركة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.menu, color: AppColors.textDark),
          onPressed: () => _scaffoldKey.currentState?.openDrawer(),
        ),
        title: const Text(AppTexts.transactions),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textDark,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddTransactionDialog,
          ),
        ],
      ),
      drawer: const AppDrawer(currentRoute: '/transactions'),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(20),
              child: Card(
                elevation: 2,
                shadowColor: Colors.black.withOpacity(0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: const BorderSide(color: AppColors.cardBorder, width: 1),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: AppColors.cardGradient,
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'البحث في الحركات...',
                      border: InputBorder.none,
                      icon:
                          const Icon(Icons.search, color: AppColors.textLight),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear,
                                  color: AppColors.textLight),
                              onPressed: () {
                                _searchController.clear();
                                setState(() => _searchQuery = '');
                                _filterTransactions();
                              },
                            )
                          : null,
                    ),
                    onChanged: (value) {
                      setState(() => _searchQuery = value);
                      _filterTransactions();
                    },
                  ),
                ),
              ),
            ),

            // Transactions list
            Expanded(
              child: SmartRefresher(
                controller: _refreshController,
                onRefresh: () => _loadData(isRefresh: true),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredTransactions.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            padding: const EdgeInsets.all(20),
                            itemCount: _filteredTransactions.length,
                            itemBuilder: (context, index) {
                              final transaction = _filteredTransactions[index];
                              final typeColor = _getTypeColor(transaction.type);

                              return Dismissible(
                                key: Key(transaction.id),
                                direction: DismissDirection.endToStart,
                                background: Container(
                                  color: AppColors.error,
                                  alignment: Alignment.centerRight,
                                  padding: const EdgeInsets.only(right: 20),
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: const Icon(Icons.delete,
                                      color: Colors.white),
                                ),
                                confirmDismiss: (_) => _showConfirmationDialog(
                                  title: 'تأكيد الحذف',
                                  content: 'هل أنت متأكد من حذف هذه الحركة؟',
                                ),
                                onDismissed: (_) =>
                                    _deleteTransaction(transaction),
                                child: Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  child: Card(
                                    elevation: 4,
                                    shadowColor: Colors.black.withOpacity(0.1),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                      side: const BorderSide(
                                          color: AppColors.cardBorder,
                                          width: 1),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(20),
                                        gradient: AppColors.cardGradient,
                                      ),
                                      child: ListTile(
                                        contentPadding:
                                            const EdgeInsets.all(20),
                                        leading: Container(
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color: typeColor.withOpacity(0.1),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Icon(
                                            _getTypeIcon(transaction.type),
                                            color: typeColor,
                                            size: 24,
                                          ),
                                        ),
                                        title: Text(
                                          transaction.itemName,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w600,
                                                color: AppColors.textDark,
                                              ),
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const SizedBox(height: 8),
                                            if (transaction.notes?.isNotEmpty ??
                                                false)
                                              Text(
                                                transaction.notes!,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.copyWith(
                                                      color:
                                                          AppColors.textLight,
                                                    ),
                                              ),
                                            const SizedBox(height: 12),
                                            Wrap(
                                              spacing: 8,
                                              runSpacing: 8,
                                              children: [
                                                _InfoChip(
                                                  icon: Icons.swap_horiz,
                                                  label:
                                                      _getTransactionTypeText(
                                                          transaction.type),
                                                  color: typeColor,
                                                ),
                                                _InfoChip(
                                                  icon: Icons.inventory,
                                                  label:
                                                      '${transaction.quantity} ${transaction.unit}',
                                                  color: AppColors.success,
                                                ),
                                                _InfoChip(
                                                  icon: Icons.calendar_today,
                                                  label: _formatDate(
                                                      transaction.createdAt),
                                                  color: AppColors.info,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddTransactionDialog,
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textInverse,
        icon: const Icon(Icons.add),
        label: const Text('حركة جديدة'),
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: AppColors.textLight.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.swap_horiz_outlined,
                size: 64,
                color: AppColors.textLight,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد حركات',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDark,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بتسجيل حركات جديدة للمخزون',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textLight,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _showAddTransactionDialog,
              icon: const Icon(Icons.add),
              label: const Text('حركة جديدة'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    quantityController.dispose();
    responsibleController.dispose();
    notesController.dispose();
    requestNumberController.dispose();
    centralController.dispose();
    recipientNameController.dispose();
    spendingReasonController.dispose();
    locationController.dispose();
    _searchController.dispose();
    _refreshController.dispose();
    super.dispose();
  }
}

class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _InfoChip({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
