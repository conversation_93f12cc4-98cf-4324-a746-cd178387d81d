import 'package:flutter/material.dart';
import '../../constants/app_texts.dart';
import '../../constants/app_colors.dart';
import '../../widgets/app_drawer.dart';
import '../../services/database_service.dart';
import '../../models/transaction.dart';

class DashboardView extends StatefulWidget {
  const DashboardView({Key? key}) : super(key: key);

  @override
  _DashboardViewState createState() => _DashboardViewState();
}

class _DashboardViewState extends State<DashboardView> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final DatabaseService _databaseService = DatabaseService();

  // بيانات الإحصائيات
  Map<String, dynamic> _stats = {
    'totalItems': 0,
    'totalQuantity': 0.0,
    'lowStockItems': 0,
    'categories': 0,
  };

  // بيانات الرسوم البيانية
  List<Map<String, dynamic>> _topUsedItems = [];
  List<Map<String, dynamic>> _stockDistribution = [];
  List<Transaction> _recentTransactions = [];

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تحديث الإحصائيات عند العودة إلى الصفحة
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);
    try {
      // جلب الإحصائيات الأساسية
      final stats = await _databaseService.getDashboardStats();

      // جلب بيانات الرسوم البيانية
      final topUsedItems = await _databaseService.getTopUsedItems(5);
      final stockDistribution = await _databaseService.getStockDistribution();

      setState(() {
        _stats = stats;
        _topUsedItems = topUsedItems;
        _stockDistribution = stockDistribution;
        _recentTransactions =
            List<Transaction>.from(stats['recentTransactions'] ?? []);
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل بيانات لوحة التحكم: $e');
      setState(() => _isLoading = false);
    }
  }

  // دالة تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final isMediumScreen = screenSize.width >= 600 && screenSize.width < 1200;

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.menu, color: AppColors.textDark),
          onPressed: () => _scaffoldKey.currentState?.openDrawer(),
        ),
        title: Text(AppTexts.dashboard),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textDark,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: إضافة الإشعارات
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {
              // TODO: إضافة الإعدادات
            },
          ),
        ],
      ),
      drawer: const AppDrawer(currentRoute: '/dashboard'),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ===== عنوان الترحيب =====
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: AppColors.cardShadow,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.textInverse.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Icon(
                            Icons.dashboard,
                            color: AppColors.textInverse,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مرحباً بك في نظام إدارة المخزون',
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineSmall
                                    ?.copyWith(
                                      color: AppColors.textInverse,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'نظرة عامة على حالة المخزون والحركات',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge
                                    ?.copyWith(
                                      color: AppColors.textInverse
                                          .withValues(alpha: 0.8),
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),

              // ===== إحصائيات سريعة =====
              Text(
                'الإحصائيات السريعة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
              ),
              const SizedBox(height: 20),

              // ===== بطاقات الإحصائيات =====
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: isSmallScreen ? 2 : (isMediumScreen ? 3 : 4),
                crossAxisSpacing: 20,
                mainAxisSpacing: 20,
                childAspectRatio: isSmallScreen ? 1.1 : 1.3,
                children: [
                  _StatCard(
                    title: AppTexts.totalItems,
                    value: _stats['totalItems'].toString(),
                    subtitle: 'أصناف',
                    icon: Icons.inventory_2,
                    gradient: AppColors.primaryGradient,
                  ),
                  _StatCard(
                    title: AppTexts.totalQuantity,
                    value: _stats['totalQuantity'].toString(),
                    subtitle: 'وحدة',
                    icon: Icons.analytics,
                    gradient: AppColors.successGradient,
                  ),
                  _StatCard(
                    title: AppTexts.lowStockItems,
                    value: _stats['lowStockItems'].toString(),
                    subtitle: 'أصناف',
                    icon: Icons.warning,
                    gradient: AppColors.warningGradient,
                  ),
                  _StatCard(
                    title: AppTexts.categories,
                    value: _stats['categories'].toString(),
                    subtitle: 'فئات',
                    icon: Icons.category,
                    gradient: AppColors.secondaryGradient,
                  ),
                ],
              ),
              const SizedBox(height: 40),

              // ===== الرسوم البيانية =====
              Row(
                children: [
                  Icon(
                    Icons.bar_chart,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'تحليل المخزون',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // ===== بطاقة الرسوم البيانية =====
              Card(
                elevation: 4,
                shadowColor: Colors.black.withValues(alpha: 0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                  side: BorderSide(
                    color: AppColors.cardBorder,
                    width: 1,
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    gradient: AppColors.cardGradient,
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'أفضل 5 أصناف حسب الاستخدام',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textDark,
                            ),
                      ),
                      const SizedBox(height: 24),

                      // رسم بياني شريطي بسيط
                      _BarChart(data: _topUsedItems),

                      const SizedBox(height: 24),

                      // رسم بياني دائري بسيط
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'توزيع المخزون',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.textDark,
                                      ),
                                ),
                                const SizedBox(height: 16),
                                _PieChart(data: _stockDistribution),
                              ],
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: _ChartLegend(data: _stockDistribution),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 40),

              // ===== قسم آخر الحركات =====
              Row(
                children: [
                  Icon(
                    Icons.history,
                    color: AppColors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    AppTexts.recentTransactions,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // ===== بطاقة آخر الحركات =====
              Card(
                elevation: 4,
                shadowColor: Colors.black.withValues(alpha: 0.1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                  side: BorderSide(
                    color: AppColors.cardBorder,
                    width: 1,
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    gradient: AppColors.cardGradient,
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: _recentTransactions.isEmpty
                        ? [
                            const SizedBox(height: 40),
                            Icon(
                              Icons.history,
                              size: 48,
                              color: AppColors.textLight.withValues(alpha: 0.5),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد حركات حديثة',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: AppColors.textLight,
                                  ),
                            ),
                            const SizedBox(height: 40),
                          ]
                        : _recentTransactions.map((transaction) {
                            final isLast =
                                _recentTransactions.last == transaction;
                            return Column(
                              children: [
                                _TransactionItem(
                                  itemName: transaction.itemName,
                                  type: transaction.typeDisplayName,
                                  quantity:
                                      '${transaction.quantity} ${transaction.unit}',
                                  date: _formatDate(transaction.date),
                                  typeColor: transaction.isIncoming
                                      ? AppColors.incoming
                                      : AppColors.outgoing,
                                ),
                                if (!isLast) const Divider(height: 32),
                              ],
                            );
                          }).toList(),
                  ),
                ),
              ),
              const SizedBox(height: 40),

              // ===== قسم الإجراءات السريعة =====
              Text(
                'الإجراءات السريعة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
              ),
              const SizedBox(height: 20),

              // ===== أزرار الإجراءات السريعة =====
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: isSmallScreen ? 2 : 3,
                crossAxisSpacing: 20,
                mainAxisSpacing: 20,
                childAspectRatio: 2.5,
                children: [
                  _ActionButton(
                    title: 'إضافة صنف',
                    subtitle: 'إضافة صنف جديد',
                    icon: Icons.add_box,
                    color: AppColors.success,
                    onTap: () {
                      Navigator.pushNamed(context, '/items');
                    },
                  ),
                  _ActionButton(
                    title: 'حركة جديدة',
                    subtitle: 'تسجيل حركة',
                    icon: Icons.swap_horiz,
                    color: AppColors.primary,
                    onTap: () {
                      Navigator.pushNamed(context, '/transactions');
                    },
                  ),
                  _ActionButton(
                    title: 'التقارير',
                    subtitle: 'عرض التقارير',
                    icon: Icons.assessment,
                    color: AppColors.info,
                    onTap: () {
                      Navigator.pushNamed(context, '/reports');
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ===== بطاقة الإحصائيات =====
class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final LinearGradient gradient;

  const _StatCard({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
        side: BorderSide(
          color: AppColors.cardBorder,
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          gradient: gradient,
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.textInverse.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppColors.textInverse,
                size: 28,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textInverse,
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.textInverse.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textInverse.withValues(alpha: 0.8),
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// ===== رسم بياني شريطي بسيط =====
class _BarChart extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const _BarChart({required this.data});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bar_chart,
                size: 48,
                color: AppColors.textLight.withValues(alpha: 0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد بيانات للعرض',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.textLight,
                    ),
              ),
            ],
          ),
        ),
      );
    }

    // حساب أعلى قيمة لتطبيع البيانات
    final maxValue = data.fold<double>(0.0, (max, item) {
      final quantity = (item['totalQuantity'] as double?) ?? 0.0;
      return quantity > max ? quantity : max;
    });

    // ألوان للرسوم البيانية
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.info,
      AppColors.secondary,
    ];

    return SizedBox(
      height: 200,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: data.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final quantity = (item['totalQuantity'] as double?) ?? 0.0;
          final normalizedValue = maxValue > 0 ? quantity / maxValue : 0.0;
          final color = colors[index % colors.length];

          return _BarItem(
            label: (item['itemName'] as String?) ?? 'غير محدد',
            value: normalizedValue,
            color: color,
            actualValue: quantity.toStringAsFixed(1),
          );
        }).toList(),
      ),
    );
  }
}

class _BarItem extends StatelessWidget {
  final String label;
  final double value;
  final Color color;
  final String? actualValue;

  const _BarItem({
    required this.label,
    required this.value,
    required this.color,
    this.actualValue,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 40,
          height: 160 * value,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight,
                fontWeight: FontWeight.w500,
              ),
        ),
      ],
    );
  }
}

// ===== رسم بياني دائري بسيط =====
class _PieChart extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const _PieChart({required this.data});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return SizedBox(
        width: 120,
        height: 120,
        child: Center(
          child: Icon(
            Icons.pie_chart,
            size: 48,
            color: AppColors.textLight.withValues(alpha: 0.5),
          ),
        ),
      );
    }

    return SizedBox(
      width: 120,
      height: 120,
      child: Stack(
        children: [
          // دائرة خلفية
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: AppColors.cardBorder, width: 8),
            ),
          ),
          // شرائح الدائرة
          CustomPaint(
            size: const Size(120, 120),
            painter: PieChartPainter(data: data),
          ),
        ],
      ),
    );
  }
}

class PieChartPainter extends CustomPainter {
  final List<Map<String, dynamic>> data;

  PieChartPainter({required this.data});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 4;

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 8;

    double startAngle = 0;
    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.info,
      AppColors.secondary,
    ];

    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final percentage = (item['percentage'] as double?) ?? 0.0;
      final color = colors[i % colors.length];

      paint.color = color;
      final sweepAngle = 2 * 3.14159 * percentage;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// ===== مفتاح الرسم البياني =====
class _ChartLegend extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const _ChartLegend({required this.data});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التوزيع',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textDark,
                ),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بيانات',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textLight,
                ),
          ),
        ],
      );
    }

    final colors = [
      AppColors.primary,
      AppColors.success,
      AppColors.warning,
      AppColors.info,
      AppColors.secondary,
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التوزيع',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textDark,
              ),
        ),
        const SizedBox(height: 16),
        ...data.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final categoryName = (item['categoryName'] as String?) ?? 'غير محدد';
          final percentage = (item['percentage'] as double?) ?? 0.0;
          final color = colors[index % colors.length];

          return Column(
            children: [
              _LegendItem(
                label:
                    '$categoryName (${(percentage * 100).toStringAsFixed(1)}%)',
                color: color,
              ),
              if (index < data.length - 1) const SizedBox(height: 8),
            ],
          );
        }).toList(),
      ],
    );
  }
}

class _LegendItem extends StatelessWidget {
  final String label;
  final Color color;

  const _LegendItem({
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textLight,
              ),
        ),
      ],
    );
  }
}

// ===== عنصر الحركة =====
class _TransactionItem extends StatelessWidget {
  final String itemName;
  final String type;
  final String quantity;
  final String date;
  final Color typeColor;

  const _TransactionItem({
    required this.itemName,
    required this.type,
    required this.quantity,
    required this.date,
    required this.typeColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: typeColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            type == 'وارد'
                ? Icons.arrow_downward
                : type == 'صادر'
                    ? Icons.arrow_upward
                    : Icons.remove,
            color: typeColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                itemName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                date,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textLight,
                    ),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: typeColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                type,
                style: TextStyle(
                  color: typeColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              quantity,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
            ),
          ],
        ),
      ],
    );
  }
}

// ===== زر الإجراء =====
class _ActionButton extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  const _ActionButton({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColors.cardBorder,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.textDark,
                          ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textLight,
                          ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: color,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
