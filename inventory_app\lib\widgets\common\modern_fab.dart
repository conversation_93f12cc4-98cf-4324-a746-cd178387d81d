import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';

enum FABVariant {
  primary,
  secondary,
  extended,
}

class ModernFAB extends StatefulWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? label;
  final FABVariant variant;
  final bool isExtended;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? tooltip;

  const ModernFAB({
    Key? key,
    required this.onPressed,
    required this.icon,
    this.label,
    this.variant = FABVariant.primary,
    this.isExtended = false,
    this.backgroundColor,
    this.foregroundColor,
    this.tooltip,
  }) : super(key: key);

  const ModernFAB.primary({
    Key? key,
    required VoidCallback? onPressed,
    required IconData icon,
    String? label,
    bool isExtended = false,
    String? tooltip,
  }) : this(
          key: key,
          onPressed: onPressed,
          icon: icon,
          label: label,
          variant: FABVariant.primary,
          isExtended: isExtended,
          tooltip: tooltip,
        );

  const ModernFAB.secondary({
    Key? key,
    required VoidCallback? onPressed,
    required IconData icon,
    String? label,
    bool isExtended = false,
    String? tooltip,
  }) : this(
          key: key,
          onPressed: onPressed,
          icon: icon,
          label: label,
          variant: FABVariant.secondary,
          isExtended: isExtended,
          tooltip: tooltip,
        );

  const ModernFAB.extended({
    Key? key,
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    String? tooltip,
  }) : this(
          key: key,
          onPressed: onPressed,
          icon: icon,
          label: label,
          variant: FABVariant.extended,
          isExtended: true,
          tooltip: tooltip,
        );

  @override
  State<ModernFAB> createState() => _ModernFABState();
}

class _ModernFABState extends State<ModernFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppSpacing.animationMedium,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    final colors = _getColors();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: GestureDetector(
              onTapDown: widget.onPressed != null ? _onTapDown : null,
              onTapUp: widget.onPressed != null ? _onTapUp : null,
              onTapCancel: widget.onPressed != null ? _onTapCancel : null,
              child: _buildFAB(context, isArabic, colors),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFAB(BuildContext context, bool isArabic, _FABColors colors) {
    if (widget.isExtended && widget.label != null) {
      return FloatingActionButton.extended(
        onPressed: widget.onPressed,
        backgroundColor: colors.backgroundColor,
        foregroundColor: colors.foregroundColor,
        elevation: AppSpacing.elevation3,
        tooltip: widget.tooltip,
        icon: Icon(widget.icon),
        label: Text(
          widget.label!,
          style: AppTypography.labelLarge(context, isArabic: isArabic).copyWith(
            color: colors.foregroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    return FloatingActionButton(
      onPressed: widget.onPressed,
      backgroundColor: colors.backgroundColor,
      foregroundColor: colors.foregroundColor,
      elevation: AppSpacing.elevation3,
      tooltip: widget.tooltip ?? widget.label,
      child: Icon(
        widget.icon,
        size: AppSpacing.iconMd,
      ),
    );
  }

  _FABColors _getColors() {
    switch (widget.variant) {
      case FABVariant.primary:
        return _FABColors(
          backgroundColor: widget.backgroundColor ?? AppColors.primary,
          foregroundColor: widget.foregroundColor ?? AppColors.onPrimary,
        );
      case FABVariant.secondary:
        return _FABColors(
          backgroundColor: widget.backgroundColor ?? AppColors.secondary,
          foregroundColor: widget.foregroundColor ?? AppColors.onSecondary,
        );
      case FABVariant.extended:
        return _FABColors(
          backgroundColor: widget.backgroundColor ?? AppColors.primary,
          foregroundColor: widget.foregroundColor ?? AppColors.onPrimary,
        );
    }
  }
}

class _FABColors {
  final Color backgroundColor;
  final Color foregroundColor;

  const _FABColors({
    required this.backgroundColor,
    required this.foregroundColor,
  });
}

// Specialized FAB components
class AddItemFAB extends StatelessWidget {
  final VoidCallback? onPressed;

  const AddItemFAB({
    Key? key,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    
    return ModernFAB.extended(
      onPressed: onPressed,
      icon: Icons.add,
      label: isArabic ? 'إضافة صنف' : 'Add Item',
      tooltip: isArabic ? 'إضافة صنف جديد' : 'Add new item',
    );
  }
}

class QuickActionFAB extends StatefulWidget {
  final List<QuickAction> actions;

  const QuickActionFAB({
    Key? key,
    required this.actions,
  }) : super(key: key);

  @override
  State<QuickActionFAB> createState() => _QuickActionFABState();
}

class _QuickActionFABState extends State<QuickActionFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppSpacing.animationMedium,
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...widget.actions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          
          return AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              final delay = index * 0.1;
              final animationValue = (_expandAnimation.value - delay).clamp(0.0, 1.0);
              
              return Transform.scale(
                scale: animationValue,
                child: Transform.translate(
                  offset: Offset(0, (1 - animationValue) * 20),
                  child: Opacity(
                    opacity: animationValue,
                    child: Container(
                      margin: AppSpacing.paddingVerticalXs,
                      child: ModernFAB(
                        onPressed: () {
                          action.onPressed();
                          _toggle();
                        },
                        icon: action.icon,
                        tooltip: action.label,
                        variant: FABVariant.secondary,
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }).toList().reversed.toList(),
        AppSpacing.verticalSpaceSm,
        ModernFAB.primary(
          onPressed: _toggle,
          icon: _isExpanded ? Icons.close : Icons.add,
        ),
      ],
    );
  }
}

class QuickAction {
  final String label;
  final IconData icon;
  final VoidCallback onPressed;

  const QuickAction({
    required this.label,
    required this.icon,
    required this.onPressed,
  });
}
